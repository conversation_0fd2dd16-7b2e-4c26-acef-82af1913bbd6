"""
End-to-end integration tests combining shortage analysis and alert management with real services.

This module tests the complete workflow from shortage analysis through alert notification
using actual agent_develop services and real data from question.md files.
"""

import pytest
import asyncio
import time
from typing import Dict, Any, List
import json

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent
from schemas.agent_schemas import (
    ShortageAnalysisInputSchema,
    AlertManagementInputSchema
)
from tests.utils.question_parser import convert_to_shortage_schema, convert_to_alert_schema


class TestEndToEndRealServices:
    """End-to-end integration tests using real services."""

    @pytest.fixture(autouse=True)
    def setup(self, ensure_services_running):
        """Setup test environment with both services available."""
        pass

    @pytest.mark.integration
    @pytest.mark.slow
    async def test_full_workflow_real_services(
        self,
        shortage_agent,
        alert_agent,
        basic_shortage_data,
        http_alert_data,
        performance_timer
    ):
        """Test complete workflow from shortage analysis to alert notification."""
        performance_timer.start()
        
        # Step 1: Perform shortage analysis using real service
        shortage_input = ShortageAnalysisInputSchema(**basic_shortage_data)
        
        shortage_result = await shortage_agent.call_tool(
            name="analyze_shortage",
            arguments=shortage_input.model_dump()
        )
        
        # Validate shortage analysis result
        assert shortage_result is not None
        assert "shortage_index" in shortage_result
        assert "risk_level" in shortage_result
        
        shortage_index = shortage_result["shortage_index"]
        risk_level = shortage_result["risk_level"]
        
        # Step 2: Determine if alert should be triggered based on shortage level
        alert_threshold = 0.5  # Configurable threshold
        should_alert = False
        
        if isinstance(shortage_index, (int, float)):
            should_alert = shortage_index > alert_threshold
        elif isinstance(shortage_index, str):
            try:
                should_alert = float(shortage_index) > alert_threshold
            except ValueError:
                pytest.fail(f"Invalid shortage_index format: {shortage_index}")
        
        # Step 3: If shortage is high, trigger alerts using real service
        if should_alert:
            # Customize alert message with shortage analysis results
            enhanced_alert_data = http_alert_data.copy()
            enhanced_alert_data["alert_message"] = (
                f"HIGH SHORTAGE ALERT: Shortage index {shortage_index} "
                f"(Risk Level: {risk_level}). {enhanced_alert_data['alert_message']}"
            )
            
            alert_input = AlertManagementInputSchema(**enhanced_alert_data)
            
            alert_result = await alert_agent.call_tool(
                name="send_alert",
                arguments=alert_input.model_dump()
            )
            
            # Validate alert was sent successfully
            assert alert_result is not None
            assert "alert_id" in alert_result
            assert "delivery_status" in alert_result
            assert alert_result["delivery_status"] in ["SENT", "DELIVERED", "SUCCESS"]
            
            # Verify alert content includes shortage information
            if "alert_content" in alert_result:
                alert_content = alert_result["alert_content"].lower()
                assert str(shortage_index).lower() in alert_content
                assert risk_level.lower() in alert_content
        
        elapsed_time = performance_timer.stop()
        
        # Performance validation
        assert elapsed_time < 30.0, f"End-to-end workflow took too long: {elapsed_time}s"
        
        # Return results for further validation
        return {
            "shortage_result": shortage_result,
            "alert_result": alert_result if should_alert else None,
            "alert_triggered": should_alert,
            "elapsed_time": elapsed_time
        }

    @pytest.mark.integration
    async def test_service_chain_integration(
        self,
        shortage_agent,
        alert_agent,
        weighted_shortage_data,
        multi_channel_alert_data
    ):
        """Test integration between shortage-index and alert-notification services."""
        # Perform weighted shortage analysis
        shortage_input = ShortageAnalysisInputSchema(**weighted_shortage_data)
        
        shortage_result = await shortage_agent.call_tool(
            name="analyze_shortage",
            arguments=shortage_input.model_dump()
        )
        
        # Extract weighted shortage index
        weighted_index = shortage_result.get("weighted_shortage_index")
        if weighted_index is None:
            weighted_index = shortage_result.get("shortage_index")
        
        assert weighted_index is not None
        
        # Configure alert based on shortage analysis results
        alert_config = multi_channel_alert_data.copy()
        
        # Determine alert severity based on shortage level
        if isinstance(weighted_index, (int, float)):
            shortage_value = weighted_index
        else:
            shortage_value = float(weighted_index)
        
        if shortage_value >= 0.8:
            alert_config["severity"] = "CRITICAL"
        elif shortage_value >= 0.6:
            alert_config["severity"] = "HIGH"
        elif shortage_value >= 0.4:
            alert_config["severity"] = "MEDIUM"
        else:
            alert_config["severity"] = "LOW"
        
        # Include shortage data in alert message
        alert_config["alert_message"] = (
            f"Shortage Analysis Result: Index={shortage_value}, "
            f"Risk={shortage_result.get('risk_level', 'UNKNOWN')}. "
            f"{alert_config['alert_message']}"
        )
        
        # Send multi-channel alert
        alert_input = AlertManagementInputSchema(**alert_config)
        
        alert_result = await alert_agent.call_tool(
            name="send_multi_channel_alert",
            arguments=alert_input.model_dump()
        )
        
        # Validate service chain integration
        assert alert_result is not None
        assert "channel_results" in alert_result
        
        # Verify all channels were processed
        channel_results = alert_result["channel_results"]
        expected_channels = {"HTTP", "MQTT", "EMAIL"}
        
        if isinstance(channel_results, list):
            processed_channels = {r.get("channel") for r in channel_results}
        else:
            processed_channels = set(channel_results.keys())
        
        assert expected_channels.issubset(processed_channels)

    @pytest.mark.integration
    async def test_real_data_flow_from_questions(
        self,
        shortage_agent,
        alert_agent,
        shortage_question_data,
        alert_question_data
    ):
        """Test realistic data flow using actual question scenarios."""
        # Use all scenarios from question.md files
        for shortage_scenario in ["basic_shortage", "weighted_shortage"]:
            if shortage_scenario not in shortage_question_data:
                continue
                
            shortage_data = shortage_question_data[shortage_scenario]
            shortage_input = ShortageAnalysisInputSchema(**shortage_data)
            
            # Analyze shortage
            shortage_result = await shortage_agent.call_tool(
                name="analyze_shortage",
                arguments=shortage_input.model_dump()
            )
            
            assert shortage_result is not None
            shortage_index = shortage_result.get("shortage_index") or shortage_result.get("weighted_shortage_index")
            
            # Test alert scenarios with real question data
            for alert_scenario in ["http_alert", "mqtt_alert", "email_alert"]:
                if alert_scenario not in alert_question_data:
                    continue
                    
                alert_data = alert_question_data[alert_scenario].copy()
                
                # Enhance alert with shortage results
                alert_data["alert_message"] = (
                    f"Scenario: {shortage_scenario} -> {alert_scenario}. "
                    f"Shortage Index: {shortage_index}. "
                    f"{alert_data['alert_message']}"
                )
                
                alert_input = AlertManagementInputSchema(**alert_data)
                
                alert_result = await alert_agent.call_tool(
                    name="send_alert",
                    arguments=alert_input.model_dump()
                )
                
                # Validate each scenario combination
                assert alert_result is not None
                assert "delivery_status" in alert_result
                assert alert_result["delivery_status"] in ["SENT", "DELIVERED", "SUCCESS", "QUEUED", "PUBLISHED"]

    @pytest.mark.integration
    async def test_error_recovery_workflow(
        self,
        shortage_agent,
        alert_agent,
        basic_shortage_data,
        http_alert_data,
        monkeypatch
    ):
        """Test error recovery when one service fails during workflow."""
        # Step 1: Normal shortage analysis
        shortage_input = ShortageAnalysisInputSchema(**basic_shortage_data)
        
        shortage_result = await shortage_agent.call_tool(
            name="analyze_shortage",
            arguments=shortage_input.model_dump()
        )
        
        assert shortage_result is not None
        
        # Step 2: Simulate alert service failure
        monkeypatch.setenv("ALERT_SERVICE_URL", "http://localhost:9999")
        
        # Create new agent with failed service
        failed_alert_agent = create_alert_manager_agent()
        
        alert_input = AlertManagementInputSchema(**http_alert_data)
        
        # Alert should fail gracefully
        with pytest.raises(Exception) as exc_info:
            await failed_alert_agent.call_tool(
                name="send_alert",
                arguments=alert_input.model_dump()
            )
        
        # Verify error handling
        error_msg = str(exc_info.value).lower()
        assert any(keyword in error_msg for keyword in ["connection", "timeout", "unavailable"])
        
        # Step 3: Verify shortage analysis still works independently
        shortage_result_2 = await shortage_agent.call_tool(
            name="analyze_shortage",
            arguments=shortage_input.model_dump()
        )
        
        assert shortage_result_2 is not None
        assert shortage_result_2.get("shortage_index") == shortage_result.get("shortage_index")

    @pytest.mark.integration
    @pytest.mark.slow
    async def test_performance_under_load(
        self,
        shortage_agent,
        alert_agent,
        basic_shortage_data,
        weighted_shortage_data,
        http_alert_data,
        mqtt_alert_data,
        performance_timer
    ):
        """Test system performance under concurrent load."""
        performance_timer.start()
        
        # Create multiple concurrent workflows
        async def run_shortage_workflow(data):
            input_schema = ShortageAnalysisInputSchema(**data)
            return await shortage_agent.call_tool(
                name="analyze_shortage",
                arguments=input_schema.model_dump()
            )
        
        async def run_alert_workflow(data):
            input_schema = AlertManagementInputSchema(**data)
            return await alert_agent.call_tool(
                name="send_alert",
                arguments=input_schema.model_dump()
            )
        
        # Run concurrent workflows
        shortage_tasks = [
            run_shortage_workflow(basic_shortage_data),
            run_shortage_workflow(weighted_shortage_data),
            run_shortage_workflow(basic_shortage_data),
        ]
        
        alert_tasks = [
            run_alert_workflow(http_alert_data),
            run_alert_workflow(mqtt_alert_data),
            run_alert_workflow(http_alert_data),
        ]
        
        # Execute all tasks concurrently
        all_results = await asyncio.gather(
            *shortage_tasks,
            *alert_tasks,
            return_exceptions=True
        )
        
        elapsed_time = performance_timer.stop()
        
        # Validate all requests completed
        successful_results = []
        failed_results = []
        
        for i, result in enumerate(all_results):
            if isinstance(result, Exception):
                failed_results.append((i, result))
            else:
                successful_results.append(result)
        
        # Performance assertions
        assert len(failed_results) == 0, f"Failed requests: {failed_results}"
        assert len(successful_results) == 6, f"Expected 6 successful results, got {len(successful_results)}"
        assert elapsed_time < 60.0, f"Concurrent workflows took too long: {elapsed_time}s"
        
        # Verify result quality
        for result in successful_results:
            assert result is not None
            assert isinstance(result, dict)

    @pytest.mark.integration
    async def test_configuration_validation_workflow(
        self,
        shortage_agent,
        alert_agent,
        basic_shortage_data
    ):
        """Test workflow with different alert configurations and shortage thresholds."""
        # Test different threshold configurations
        thresholds = [0.3, 0.5, 0.7, 0.9]
        severities = ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
        
        shortage_input = ShortageAnalysisInputSchema(**basic_shortage_data)
        
        shortage_result = await shortage_agent.call_tool(
            name="analyze_shortage",
            arguments=shortage_input.model_dump()
        )
        
        shortage_index = float(shortage_result.get("shortage_index", 0))
        
        for threshold, severity in zip(thresholds, severities):
            if shortage_index > threshold:
                # Configure alert for this threshold
                alert_config = {
                    "alert_message": f"Threshold {threshold} exceeded with index {shortage_index}",
                    "channels": ["HTTP"],
                    "severity": severity,
                    "delivery_config": {
                        "http": {"url": "http://localhost:8080/alerts"}
                    }
                }
                
                alert_input = AlertManagementInputSchema(**alert_config)
                
                alert_result = await alert_agent.call_tool(
                    name="send_alert",
                    arguments=alert_input.model_dump()
                )
                
                # Validate threshold-based alerting
                assert alert_result is not None
                assert alert_result.get("delivery_status") in ["SENT", "DELIVERED", "SUCCESS"]
                
                # Verify severity mapping
                if "severity" in alert_result:
                    assert alert_result["severity"] == severity

    @pytest.mark.integration
    async def test_data_consistency_across_services(
        self,
        shortage_agent,
        alert_agent,
        weighted_shortage_data,
        multi_channel_alert_data
    ):
        """Test that data remains consistent across service calls."""
        # Perform shortage analysis multiple times
        shortage_input = ShortageAnalysisInputSchema(**weighted_shortage_data)
        
        results = []
        for _ in range(3):
            result = await shortage_agent.call_tool(
                name="analyze_shortage",
                arguments=shortage_input.model_dump()
            )
            results.append(result)
        
        # Verify consistency of results
        first_result = results[0]
        for result in results[1:]:
            assert result.get("shortage_index") == first_result.get("shortage_index")
            assert result.get("weighted_shortage_index") == first_result.get("weighted_shortage_index")
            assert result.get("risk_level") == first_result.get("risk_level")
        
        # Test alert consistency
        alert_input = AlertManagementInputSchema(**multi_channel_alert_data)
        
        alert_results = []
        for _ in range(2):
            result = await alert_agent.call_tool(
                name="send_multi_channel_alert",
                arguments=alert_input.model_dump()
            )
            alert_results.append(result)
        
        # Verify alert IDs are unique but structure is consistent
        alert_ids = [r.get("alert_id") for r in alert_results]
        assert len(set(alert_ids)) == len(alert_ids), "Alert IDs should be unique"
        
        for result in alert_results:
            assert "channel_results" in result
            assert len(result["channel_results"]) == len(alert_results[0]["channel_results"])

    @pytest.mark.integration
    async def test_end_to_end_orchestration_simulation(
        self,
        shortage_agent,
        alert_agent,
        shortage_question_data,
        alert_question_data
    ):
        """Test simulation of main.py orchestrator using real services."""
        # Simulate the main orchestrator workflow
        
        # Stage 1: Initial shortage analysis
        basic_data = shortage_question_data.get("basic_shortage", {})
        shortage_input = ShortageAnalysisInputSchema(**basic_data)
        
        initial_result = await shortage_agent.call_tool(
            name="analyze_shortage",
            arguments=shortage_input.model_dump()
        )
        
        # Stage 2: Decision making based on results
        shortage_index = float(initial_result.get("shortage_index", 0))
        risk_level = initial_result.get("risk_level", "LOW")
        
        # Stage 3: Conditional alert triggering
        alerts_sent = []
        
        if shortage_index > 0.7:  # Critical threshold
            # Send all alert types
            for alert_type in ["http_alert", "mqtt_alert", "email_alert"]:
                if alert_type in alert_question_data:
                    alert_data = alert_question_data[alert_type].copy()
                    alert_data["alert_message"] = f"CRITICAL: {alert_data['alert_message']}"
                    alert_data["severity"] = "CRITICAL"
                    
                    alert_input = AlertManagementInputSchema(**alert_data)
                    result = await alert_agent.call_tool(
                        name="send_alert",
                        arguments=alert_input.model_dump()
                    )
                    alerts_sent.append(result)
        
        elif shortage_index > 0.5:  # High threshold
            # Send HTTP alert only
            if "http_alert" in alert_question_data:
                alert_data = alert_question_data["http_alert"].copy()
                alert_data["severity"] = "HIGH"
                
                alert_input = AlertManagementInputSchema(**alert_data)
                result = await alert_agent.call_tool(
                    name="send_alert",
                    arguments=alert_input.model_dump()
                )
                alerts_sent.append(result)
        
        # Stage 4: Follow-up analysis with weights
        if "weighted_shortage" in shortage_question_data:
            weighted_data = shortage_question_data["weighted_shortage"]
            weighted_input = ShortageAnalysisInputSchema(**weighted_data)
            
            weighted_result = await shortage_agent.call_tool(
                name="analyze_shortage",
                arguments=weighted_input.model_dump()
            )
            
            # Compare basic vs weighted results
            weighted_index = float(weighted_result.get("weighted_shortage_index", weighted_result.get("shortage_index", 0)))
            
            if abs(weighted_index - shortage_index) > 0.1:  # Significant difference
                # Send follow-up alert
                if "mqtt_alert" in alert_question_data:
                    alert_data = alert_question_data["mqtt_alert"].copy()
                    alert_data["alert_message"] = (
                        f"Weighted analysis shows different result: "
                        f"Basic={shortage_index:.3f}, Weighted={weighted_index:.3f}"
                    )
                    
                    alert_input = AlertManagementInputSchema(**alert_data)
                    result = await alert_agent.call_tool(
                        name="send_alert",
                        arguments=alert_input.model_dump()
                    )
                    alerts_sent.append(result)
        
        # Validate orchestration results
        assert initial_result is not None
        assert "shortage_index" in initial_result
        
        if shortage_index > 0.5:
            assert len(alerts_sent) > 0, "Alerts should have been sent for high shortage"
        
        for alert_result in alerts_sent:
            assert alert_result is not None
            assert "delivery_status" in alert_result
            assert alert_result["delivery_status"] in ["SENT", "DELIVERED", "SUCCESS", "QUEUED", "PUBLISHED"]
        
        return {
            "shortage_analysis": initial_result,
            "alerts_sent": len(alerts_sent),
            "orchestration_success": True
        }