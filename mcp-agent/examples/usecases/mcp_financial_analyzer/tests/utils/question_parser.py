"""
Utility module for parsing test scenarios from question.md files.

This module provides functions to parse the shortage analysis and alert notification
test scenarios from agent_develop question.md files and convert them into structured
data for use in real service tests.
"""

import re
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from schemas.agent_schemas import (
    ShortageAnalysisInputSchema,
    AlertManagementInputSchema
)

logger = logging.getLogger(__name__)


def parse_shortage_questions(question_file_path: str) -> Dict[str, Any]:
    """
    Parse shortage analysis test scenarios from agent_develop/index/question.md.
    
    Args:
        question_file_path: Path to the question.md file
        
    Returns:
        Dictionary containing structured test data for shortage analysis
        
    Raises:
        FileNotFoundError: If question file doesn't exist
        ValueError: If question data is malformed
    """
    question_path = Path(question_file_path)
    if not question_path.exists():
        raise FileNotFoundError(f"Question file not found: {question_file_path}")
    
    try:
        content = question_path.read_text(encoding='utf-8')
        logger.debug(f"Parsing shortage questions from: {question_file_path}")
        
        # Parse basic shortage scenario
        basic_shortage = _parse_basic_shortage_scenario(content)
        
        # Parse weighted shortage scenario
        weighted_shortage = _parse_weighted_shortage_scenario(content)
        
        return {
            "basic_shortage": basic_shortage,
            "weighted_shortage": weighted_shortage
        }
        
    except Exception as e:
        logger.error(f"Error parsing shortage questions: {e}")
        raise ValueError(f"Failed to parse shortage question data: {e}")


def parse_alert_questions(question_file_path: str) -> Dict[str, Any]:
    """
    Parse alert notification test scenarios from agent_develop/notification/question.md.
    
    Args:
        question_file_path: Path to the question.md file
        
    Returns:
        Dictionary containing structured test data for alert notifications
        
    Raises:
        FileNotFoundError: If question file doesn't exist
        ValueError: If question data is malformed
    """
    question_path = Path(question_file_path)
    if not question_path.exists():
        raise FileNotFoundError(f"Question file not found: {question_file_path}")
    
    try:
        content = question_path.read_text(encoding='utf-8')
        logger.debug(f"Parsing alert questions from: {question_file_path}")
        
        # Parse HTTP alert scenario
        http_alert = _parse_http_alert_scenario(content)
        
        # Parse MQTT alert scenario
        mqtt_alert = _parse_mqtt_alert_scenario(content)
        
        # Parse email alert scenario
        email_alert = _parse_email_alert_scenario(content)
        
        return {
            "http_alert": http_alert,
            "mqtt_alert": mqtt_alert,
            "email_alert": email_alert
        }
        
    except Exception as e:
        logger.error(f"Error parsing alert questions: {e}")
        raise ValueError(f"Failed to parse alert question data: {e}")


def _parse_basic_shortage_scenario(content: str) -> Dict[str, Any]:
    """Parse the basic shortage index scenario from question content."""
    # Look for component data patterns in the content
    # Expected format: CPU (available=1, required=2), GPU (available=2, required=6), etc.
    
    components = {}
    
    # Pattern to match component data: component_name (available=X, required=Y)
    component_pattern = r'(\w+)\s*(?:\([^)]*available\s*=\s*(\d+)[^)]*required\s*=\s*(\d+)[^)]*\)|.*?available\s*[:\s]*(\d+).*?required\s*[:\s]*(\d+))'
    
    matches = re.finditer(component_pattern, content, re.IGNORECASE)
    
    for match in matches:
        component_name = match.group(1)
        # Handle two possible capture group patterns
        available = int(match.group(2) or match.group(4))
        required = int(match.group(3) or match.group(5))
        
        components[component_name] = {
            "available": available,
            "required": required
        }
    
    # If pattern matching fails, use known values from the question
    if not components:
        logger.warning("Could not parse components from content, using default values")
        components = {
            "CPU": {"available": 1, "required": 2},
            "GPU": {"available": 2, "required": 6},
            "motherboard": {"available": 1, "required": 3},
            "fans": {"available": 1, "required": 6}
        }
    
    return {
        "components": components
    }


def _parse_weighted_shortage_scenario(content: str) -> Dict[str, Any]:
    """Parse the weighted shortage index scenario from question content."""
    # Get basic components first
    basic_data = _parse_basic_shortage_scenario(content)
    
    # Look for weight information
    weights = {}
    
    # Pattern to match weights: component_name: weight_value
    weight_pattern = r'(\w+)\s*[:\s]+(\d*\.?\d+)'
    
    # Look for weight section in content
    weight_section_pattern = r'weight[s]?\s*[:\s]+(.*?)(?:\n\n|\Z)'
    weight_section_match = re.search(weight_section_pattern, content, re.IGNORECASE | re.DOTALL)
    
    if weight_section_match:
        weight_content = weight_section_match.group(1)
        weight_matches = re.finditer(weight_pattern, weight_content)
        
        for match in weight_matches:
            component_name = match.group(1)
            weight_value = float(match.group(2))
            weights[component_name] = weight_value
    
    # If no weights found, use default values from question
    if not weights:
        logger.warning("Could not parse weights from content, using default values")
        weights = {
            "CPU": 0.2,
            "GPU": 0.6,
            "motherboard": 0.1,
            "fans": 0.1
        }
    
    return {
        "components": basic_data["components"],
        "weights": weights
    }


def _parse_http_alert_scenario(content: str) -> Dict[str, Any]:
    """Parse HTTP alert scenario from question content."""
    # Look for HTTP-related alert information
    # Pattern: "Send to url. cpu matrial shortage alert..."
    
    http_pattern = r'send\s+to\s+url[.\s]+(.*?)(?:\n|$)'
    match = re.search(http_pattern, content, re.IGNORECASE)
    
    alert_message = "cpu matrial shortage alert"
    if match:
        alert_message = match.group(1).strip()
    
    # Look for URL if specified
    url_pattern = r'url\s*[:\s]+(https?://[^\s]+)'
    url_match = re.search(url_pattern, content, re.IGNORECASE)
    
    delivery_config = {
        "http": {
            "url": url_match.group(1) if url_match else "http://localhost:8080/alerts"
        }
    }
    
    return {
        "alert_message": alert_message,
        "channels": ["HTTP"],
        "severity": "HIGH",
        "delivery_config": delivery_config
    }


def _parse_mqtt_alert_scenario(content: str) -> Dict[str, Any]:
    """Parse MQTT alert scenario from question content."""
    # Look for MQTT-related alert information
    # Pattern: "Send thought mqtt. cpu matrial shortage alert..."
    
    mqtt_pattern = r'send\s+(?:thought|through)\s+mqtt[.\s]+(.*?)(?:\n|$)'
    match = re.search(mqtt_pattern, content, re.IGNORECASE)
    
    alert_message = "cpu matrial shortage alert"
    if match:
        alert_message = match.group(1).strip()
    
    # Look for topic if specified
    topic_pattern = r'topic\s*[:\s]+([^\s]+)'
    topic_match = re.search(topic_pattern, content, re.IGNORECASE)
    
    # Look for broker if specified
    broker_pattern = r'broker\s*[:\s]+([^\s]+)'
    broker_match = re.search(broker_pattern, content, re.IGNORECASE)
    
    delivery_config = {
        "mqtt": {
            "topic": topic_match.group(1) if topic_match else "alerts/shortage",
            "broker": broker_match.group(1) if broker_match else "localhost:1883"
        }
    }
    
    return {
        "alert_message": alert_message,
        "channels": ["MQTT"],
        "severity": "HIGH",
        "delivery_config": delivery_config
    }


def _parse_email_alert_scenario(content: str) -> Dict[str, Any]:
    """Parse email alert scenario from question content."""
    # Look for email-related alert information
    # Pattern: "<NAME_EMAIL>. cpu matrial shortage alert..."
    
    email_pattern = r'send\s+to\s+([^\s@]+@[^\s.]+\.[^\s]+)[.\s]+(.*?)(?:\n|$)'
    match = re.search(email_pattern, content, re.IGNORECASE)
    
    recipient = "<EMAIL>"
    alert_message = "cpu matrial shortage alert"
    
    if match:
        recipient = match.group(1).strip()
        alert_message = match.group(2).strip()
    else:
        # Alternative pattern for just the email
        email_only_pattern = r'([^\s@]+@[^\s.]+\.[^\s]+)'
        email_match = re.search(email_only_pattern, content)
        if email_match:
            recipient = email_match.group(1)
    
    delivery_config = {
        "email": {
            "recipient": recipient,
            "subject": "Material Shortage Alert"
        }
    }
    
    return {
        "alert_message": alert_message,
        "channels": ["EMAIL"],
        "severity": "HIGH",
        "delivery_config": delivery_config
    }


def convert_to_shortage_schema(shortage_data: Dict[str, Any]) -> ShortageAnalysisInputSchema:
    """
    Convert parsed shortage data to ShortageAnalysisInputSchema.
    
    Args:
        shortage_data: Parsed shortage data dictionary
        
    Returns:
        ShortageAnalysisInputSchema instance
        
    Raises:
        ValueError: If data structure is invalid
    """
    try:
        return ShortageAnalysisInputSchema(**shortage_data)
    except Exception as e:
        logger.error(f"Error converting shortage data to schema: {e}")
        raise ValueError(f"Invalid shortage data structure: {e}")


def convert_to_alert_schema(alert_data: Dict[str, Any]) -> AlertManagementInputSchema:
    """
    Convert parsed alert data to AlertManagementInputSchema.
    
    Args:
        alert_data: Parsed alert data dictionary
        
    Returns:
        AlertManagementInputSchema instance
        
    Raises:
        ValueError: If data structure is invalid
    """
    try:
        return AlertManagementInputSchema(**alert_data)
    except Exception as e:
        logger.error(f"Error converting alert data to schema: {e}")
        raise ValueError(f"Invalid alert data structure: {e}")


def validate_shortage_data(shortage_data: Dict[str, Any]) -> bool:
    """
    Validate that shortage data contains all required fields.
    
    Args:
        shortage_data: Shortage data dictionary to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        # Check required fields
        if "components" not in shortage_data:
            logger.error("Missing 'components' field in shortage data")
            return False
        
        components = shortage_data["components"]
        if not isinstance(components, dict):
            logger.error("'components' must be a dictionary")
            return False
        
        # Validate each component
        for component_name, component_data in components.items():
            if not isinstance(component_data, dict):
                logger.error(f"Component '{component_name}' data must be a dictionary")
                return False
            
            if "available" not in component_data or "required" not in component_data:
                logger.error(f"Component '{component_name}' missing 'available' or 'required' fields")
                return False
            
            if not isinstance(component_data["available"], (int, float)) or \
               not isinstance(component_data["required"], (int, float)):
                logger.error(f"Component '{component_name}' values must be numeric")
                return False
        
        # Validate weights if present
        if "weights" in shortage_data:
            weights = shortage_data["weights"]
            if not isinstance(weights, dict):
                logger.error("'weights' must be a dictionary")
                return False
            
            for component_name, weight in weights.items():
                if not isinstance(weight, (int, float)):
                    logger.error(f"Weight for '{component_name}' must be numeric")
                    return False
                
                if not 0 <= weight <= 1:
                    logger.error(f"Weight for '{component_name}' must be between 0 and 1")
                    return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating shortage data: {e}")
        return False


def validate_alert_data(alert_data: Dict[str, Any]) -> bool:
    """
    Validate that alert data contains all required fields.
    
    Args:
        alert_data: Alert data dictionary to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        # Check required fields
        required_fields = ["alert_message", "channels", "severity"]
        for field in required_fields:
            if field not in alert_data:
                logger.error(f"Missing required field '{field}' in alert data")
                return False
        
        # Validate alert message
        if not isinstance(alert_data["alert_message"], str) or not alert_data["alert_message"].strip():
            logger.error("'alert_message' must be a non-empty string")
            return False
        
        # Validate channels
        channels = alert_data["channels"]
        if not isinstance(channels, list) or not channels:
            logger.error("'channels' must be a non-empty list")
            return False
        
        valid_channels = {"HTTP", "MQTT", "EMAIL"}
        for channel in channels:
            if channel not in valid_channels:
                logger.error(f"Invalid channel '{channel}'. Must be one of: {valid_channels}")
                return False
        
        # Validate severity
        valid_severities = {"LOW", "MEDIUM", "HIGH", "CRITICAL"}
        if alert_data["severity"] not in valid_severities:
            logger.error(f"Invalid severity '{alert_data['severity']}'. Must be one of: {valid_severities}")
            return False
        
        # Validate delivery config if present
        if "delivery_config" in alert_data:
            delivery_config = alert_data["delivery_config"]
            if not isinstance(delivery_config, dict):
                logger.error("'delivery_config' must be a dictionary")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating alert data: {e}")
        return False


# Helper function for debugging
def print_parsed_data(shortage_data: Dict[str, Any], alert_data: Dict[str, Any]) -> None:
    """
    Print parsed data for debugging purposes.
    
    Args:
        shortage_data: Parsed shortage data
        alert_data: Parsed alert data
    """
    print("=== Parsed Shortage Data ===")
    for scenario, data in shortage_data.items():
        print(f"\n{scenario}:")
        if "components" in data:
            for component, values in data["components"].items():
                print(f"  {component}: available={values['available']}, required={values['required']}")
        if "weights" in data:
            print("  Weights:")
            for component, weight in data["weights"].items():
                print(f"    {component}: {weight}")
    
    print("\n=== Parsed Alert Data ===")
    for scenario, data in alert_data.items():
        print(f"\n{scenario}:")
        print(f"  Message: {data['alert_message']}")
        print(f"  Channels: {data['channels']}")
        print(f"  Severity: {data['severity']}")
        if "delivery_config" in data:
            print(f"  Config: {data['delivery_config']}")


if __name__ == "__main__":
    # Example usage for testing
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python question_parser.py <shortage_question_file> <alert_question_file>")
        sys.exit(1)
    
    shortage_file = sys.argv[1]
    alert_file = sys.argv[2]
    
    try:
        shortage_data = parse_shortage_questions(shortage_file)
        alert_data = parse_alert_questions(alert_file)
        
        print_parsed_data(shortage_data, alert_data)
        
        # Validate data
        for scenario, data in shortage_data.items():
            if validate_shortage_data(data):
                print(f"✓ {scenario} data is valid")
            else:
                print(f"✗ {scenario} data is invalid")
        
        for scenario, data in alert_data.items():
            if validate_alert_data(data):
                print(f"✓ {scenario} data is valid")
            else:
                print(f"✗ {scenario} data is invalid")
                
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)