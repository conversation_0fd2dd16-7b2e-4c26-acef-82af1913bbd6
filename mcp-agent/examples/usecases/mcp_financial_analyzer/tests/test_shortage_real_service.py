"""
Tests for shortage analyzer agent using real agent_develop shortage-index service.

This module tests the shortage analyzer agent with actual service calls to
the shortage-index service running on localhost:6970, using real data from
agent_develop/index/question.md.
"""

import pytest
import asyncio
from typing import Dict, Any, Optional
import json

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from config.integration_config import check_service_health
from schemas.agent_schemas import ShortageAnalysisInputSchema


class TestShortageRealService:
    """Test shortage analyzer agent with real service integration."""

    @pytest.fixture(autouse=True)
    def setup(self, shortage_service_health):
        """Setup test environment with service health check."""
        if not shortage_service_health:
            pytest.skip("Shortage-index service not available")

    @pytest.mark.asyncio
    @pytest.mark.real_service
    async def test_basic_shortage_index_real_service(self, basic_shortage_data, shortage_agent):
        """Test basic shortage index calculation using real service."""
        # Convert component data to the format expected by the MCP tool
        components = basic_shortage_data["components"]
        required_qty = [comp["required"] for comp in components.values()]
        available_qty = [comp["available"] for comp in components.values()]

        # Call the real agent which connects to actual MCP server
        result = await shortage_agent.call_tool(
            name="ShortageIndex",
            arguments={
                "required_qty": required_qty,
                "available_qty": available_qty
            }
        )
        
        # Validate real service response
        assert result is not None

        # Extract the actual response from the CallToolResult
        if hasattr(result, 'content') and result.content:
            # Parse the JSON response from the tool
            import json
            response_text = result.content[0].text
            response_data = json.loads(response_text)
        else:
            response_data = result

        # Validate shortage index calculation from real service
        assert "shortage_index" in response_data
        shortage_index = response_data["shortage_index"]
        assert isinstance(shortage_index, (int, float))
        assert 0 <= shortage_index <= 1

        # Validate no error occurred
        assert response_data.get("error") is None

    @pytest.mark.real_service
    async def test_weighted_shortage_index_real_service(self, weighted_shortage_data, shortage_agent):
        """Test weighted shortage index calculation using real service."""
        # Convert component data to the format expected by the MCP tool
        components = weighted_shortage_data["components"]
        weights = weighted_shortage_data["weights"]

        required_qty = [comp["required"] for comp in components.values()]
        available_qty = [comp["available"] for comp in components.values()]
        weight_list = [weights[comp_name] for comp_name in components.keys()]

        # Call the real agent with weighted data
        result = await shortage_agent.call_tool(
            name="WeightedShortageIndex",
            arguments={
                "required_qty": required_qty,
                "available_qty": available_qty,
                "weight": weight_list
            }
        )
        
        # Validate weighted calculation response
        assert result is not None

        # Extract the actual response from the CallToolResult
        if hasattr(result, 'content') and result.content:
            # Parse the JSON response from the tool
            import json
            response_text = result.content[0].text
            response_data = json.loads(response_text)
        else:
            response_data = result

        # Validate weighted shortage index calculation from real service
        assert "weighted_shortage_index" in response_data
        weighted_index = response_data["weighted_shortage_index"]
        assert isinstance(weighted_index, (int, float))
        assert 0 <= weighted_index <= 1

        # Validate no error occurred
        assert response_data.get("error") is None

    @pytest.mark.real_service
    async def test_service_health_check(self, shortage_service_url):
        """Test that shortage-index service is healthy and responding."""
        health_status = await check_service_health(shortage_service_url)
        assert health_status is True
        
        # Additional health check with direct HTTP call
        import aiohttp
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{shortage_service_url}/health", timeout=5) as response:
                    assert response.status == 200
                    health_data = await response.json()
                    assert health_data.get("status") == "healthy"
            except Exception as e:
                pytest.fail(f"Service health check failed: {e}")

    @pytest.mark.real_service
    async def test_service_error_handling(self, shortage_agent):
        """Test error handling when service returns errors."""
        # Test with invalid data that should cause service error
        invalid_data = ShortageAnalysisInputSchema(
            components={
                "invalid_component": {"available": -1, "required": -1}
            }
        )
        
        # Should handle service errors gracefully
        with pytest.raises(Exception) as exc_info:
            await shortage_agent.call_tool(
                name="ShortageIndex",
                arguments=invalid_data.model_dump()
            )
        
        # Verify error is properly propagated
        assert "error" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()

    @pytest.mark.real_service
    async def test_service_unavailable_handling(self, monkeypatch):
        """Test behavior when shortage-index service is unavailable."""
        # Mock service URL to point to unavailable endpoint
        monkeypatch.setenv("SHORTAGE_SERVICE_URL", "http://localhost:9999")
        
        # Create agent with unavailable service
        agent = create_shortage_analyzer_agent()
        
        # Test should fail gracefully
        basic_data = {
            "components": {
                "CPU": {"available": 1, "required": 2}
            }
        }
        
        with pytest.raises(Exception) as exc_info:
            await agent.call_tool(
                name="ShortageIndex",
                arguments=basic_data
            )
        
        # Verify connection error is handled
        error_msg = str(exc_info.value).lower()
        assert any(keyword in error_msg for keyword in ["connection", "timeout", "unavailable", "refused"])

    @pytest.mark.real_service
    async def test_real_agent_mcp_integration(self, shortage_agent, basic_shortage_data):
        """Test that agent properly integrates with MCP server for real calls."""
        input_data = ShortageAnalysisInputSchema(**basic_shortage_data)
        
        # Verify agent has MCP client configured
        assert hasattr(shortage_agent, '_client')
        assert shortage_agent._client is not None
        
        # Test MCP tool call routing
        result = await shortage_agent.call_tool(
            name="ShortageIndex",
            arguments=input_data.model_dump()
        )
        
        # Verify result comes from real MCP server call
        assert result is not None
        assert isinstance(result, dict)
        
        # Validate that this is real data, not mocked
        # Real service should return specific calculation precision
        if "shortage_index" in result:
            shortage_index = result["shortage_index"]
            # Real calculations should have decimal precision
            assert isinstance(shortage_index, float) or (
                isinstance(shortage_index, str) and "." in shortage_index
            )

    @pytest.mark.real_service
    async def test_concurrent_shortage_requests(self, shortage_agent, basic_shortage_data, weighted_shortage_data):
        """Test multiple concurrent requests to real service."""
        input_basic = ShortageAnalysisInputSchema(**basic_shortage_data)
        input_weighted = ShortageAnalysisInputSchema(**weighted_shortage_data)
        
        # Execute concurrent requests
        tasks = [
            shortage_agent.call_tool(name="ShortageIndex", arguments=input_basic.model_dump()),
            shortage_agent.call_tool(name="ShortageIndex", arguments=input_weighted.model_dump()),
            shortage_agent.call_tool(name="ShortageIndex", arguments=input_basic.model_dump())
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all requests completed successfully
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                pytest.fail(f"Request {i} failed: {result}")
            
            assert result is not None
            assert "shortage_index" in result
            assert "risk_level" in result

    @pytest.mark.real_service
    async def test_question_data_accuracy(self, basic_shortage_data, weighted_shortage_data):
        """Test that parsed question data matches expected values from question.md."""
        # Validate basic shortage data structure
        assert "components" in basic_shortage_data
        components = basic_shortage_data["components"]
        
        # Verify specific values from agent_develop/index/question.md
        expected_basic = {
            "CPU": {"available": 1, "required": 2},
            "GPU": {"available": 2, "required": 6},
            "motherboard": {"available": 1, "required": 3},
            "fans": {"available": 1, "required": 6}
        }
        
        for component, values in expected_basic.items():
            assert component in components
            assert components[component]["available"] == values["available"]
            assert components[component]["required"] == values["required"]
        
        # Validate weighted data includes weights
        assert "weights" in weighted_shortage_data or any(
            "weight" in str(weighted_shortage_data).lower() for _ in [1]
        )