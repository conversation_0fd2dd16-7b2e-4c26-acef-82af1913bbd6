"""
Tests for alert manager agent using real agent_develop alert-notification service.

This module tests the alert manager agent with actual service calls to
the alert-notification service running on localhost:6971, using real data from
agent_develop/notification/question.md.
"""

import pytest
import asyncio
from typing import Dict, Any, List
import json
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.alert_manager_agent import create_alert_manager_agent
from config.integration_config import check_service_health
from schemas.agent_schemas import AlertManagementInputSchema


class TestAlertRealService:
    """Test alert manager agent with real service integration."""

    @pytest.fixture(autouse=True)
    def setup(self, alert_service_health):
        """Setup test environment with service health check."""
        if not alert_service_health:
            pytest.skip("Alert-notification service not available")

    @pytest.mark.real_service
    async def test_http_alert_real_service(self, http_alert_data, alert_agent):
        """Test HTTP alert notification using real service."""
        # Convert alert data to the format expected by the MCP tool
        alert_message = http_alert_data["alert_message"]

        # Call the real agent which connects to actual MCP server
        result = await alert_agent.call_tool(
            name="HttpNotification",
            arguments={
                "subject": "Material Shortage Alert",
                "content": alert_message
            }
        )
        
        # Validate real service response
        assert result is not None

        # Extract the actual response from the CallToolResult
        if hasattr(result, 'content') and result.content:
            # The HttpTool returns a simple string message
            response_text = result.content[0].text
            assert "✅ HTTP notification sent successfully" in response_text
        else:
            # Direct response
            assert "✅ HTTP notification sent successfully" in str(result)

    @pytest.mark.real_service
    async def test_mqtt_alert_real_service(self, mqtt_alert_data, alert_agent):
        """Test MQTT alert notification using real service."""
        # Create input schema from parsed question data
        input_data = AlertManagementInputSchema(**mqtt_alert_data)
        
        # Call the real agent for MQTT notification
        result = await alert_agent.call_tool(
            name="HttpNotification",
            arguments=input_data.model_dump()
        )
        
        # Validate MQTT-specific response
        assert result is not None
        assert "alert_id" in result
        assert "delivery_status" in result
        assert "channel" in result
        
        assert result["channel"] == "MQTT"
        assert result["delivery_status"] in ["PUBLISHED", "SENT", "SUCCESS"]
        
        # Validate MQTT delivery details
        if "delivery_details" in result:
            delivery_details = result["delivery_details"]
            assert "topic" in delivery_details or "mqtt_topic" in delivery_details
            assert "broker" in delivery_details or "mqtt_broker" in delivery_details
        
        # Validate message content from question.md
        if "message_payload" in result:
            payload = result["message_payload"].lower()
            assert "cpu" in payload
            assert "material" in payload or "matrial" in payload
            assert "shortage" in payload

    @pytest.mark.real_service
    async def test_email_alert_real_service(self, email_alert_data, alert_agent):
        """Test email alert notification using real service."""
        # Create input schema from parsed question data
        input_data = AlertManagementInputSchema(**email_alert_data)
        
        # Call the real agent for email notification
        result = await alert_agent.call_tool(
            name="HttpNotification",
            arguments=input_data.model_dump()
        )
        
        # Validate email-specific response
        assert result is not None
        assert "alert_id" in result
        assert "delivery_status" in result
        assert "channel" in result
        
        assert result["channel"] == "EMAIL"
        assert result["delivery_status"] in ["SENT", "QUEUED", "DELIVERED", "SUCCESS"]
        
        # Validate email delivery details
        if "delivery_details" in result:
            delivery_details = result["delivery_details"]
            assert "recipient" in delivery_details or "to" in delivery_details
            
            # Verify specific email from question.md
            recipient = delivery_details.get("recipient") or delivery_details.get("to")
            assert "<EMAIL>" in str(recipient)
        
        # Validate email content structure
        if "email_content" in result:
            email_content = result["email_content"]
            assert "subject" in email_content or "Subject" in str(email_content)
            assert "body" in email_content or "message" in email_content

    @pytest.mark.real_service
    async def test_alert_service_health_check(self, alert_service_url):
        """Test that alert-notification service is healthy and responding."""
        health_status = await check_service_health(alert_service_url)
        assert health_status is True
        
        # Additional health check with direct HTTP call
        import aiohttp
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{alert_service_url}/health", timeout=5) as response:
                    assert response.status == 200
                    health_data = await response.json()
                    assert health_data.get("status") == "healthy"
            except Exception as e:
                pytest.fail(f"Alert service health check failed: {e}")

    @pytest.mark.real_service
    async def test_multi_channel_alert_real_service(self, multi_channel_alert_data, alert_agent):
        """Test sending alerts through multiple channels simultaneously."""
        # Create input data for multi-channel alert
        input_data = AlertManagementInputSchema(**multi_channel_alert_data)
        
        # Call agent for multi-channel notification
        result = await alert_agent.call_tool(
            name="HttpNotification",
            arguments=input_data.model_dump()
        )
        
        # Validate multi-channel response
        assert result is not None
        assert "alert_id" in result
        assert "channel_results" in result
        
        channel_results = result["channel_results"]
        assert isinstance(channel_results, (list, dict))
        
        # Verify all requested channels were attempted
        expected_channels = {"HTTP", "MQTT", "EMAIL"}
        if isinstance(channel_results, list):
            delivered_channels = {r.get("channel") for r in channel_results}
        else:
            delivered_channels = set(channel_results.keys())
        
        assert expected_channels.issubset(delivered_channels)
        
        # Verify each channel delivery status
        for channel in expected_channels:
            if isinstance(channel_results, list):
                channel_result = next((r for r in channel_results if r.get("channel") == channel), None)
            else:
                channel_result = channel_results.get(channel)
            
            assert channel_result is not None
            assert "delivery_status" in channel_result
            assert channel_result["delivery_status"] in ["SENT", "DELIVERED", "SUCCESS", "QUEUED", "PUBLISHED"]

    @pytest.mark.real_service
    async def test_alert_error_handling(self, alert_agent):
        """Test error handling when alert service returns errors."""
        # Test with invalid alert data
        invalid_data = AlertManagementInputSchema(
            alert_message="",  # Empty message should cause error
            channels=[],  # No channels specified
            severity="INVALID_SEVERITY"
        )
        
        # Should handle service errors gracefully
        with pytest.raises(Exception) as exc_info:
            await alert_agent.call_tool(
                name="HttpNotification",
                arguments=invalid_data.model_dump()
            )
        
        # Verify error is properly propagated
        error_msg = str(exc_info.value).lower()
        assert any(keyword in error_msg for keyword in ["error", "invalid", "empty", "required"])

    @pytest.mark.real_service
    async def test_alert_service_unavailable_handling(self, monkeypatch):
        """Test behavior when alert-notification service is unavailable."""
        # Mock service URL to point to unavailable endpoint
        monkeypatch.setenv("ALERT_SERVICE_URL", "http://localhost:9998")
        
        # Create agent with unavailable service
        agent = create_alert_manager_agent()
        
        # Test should fail gracefully
        alert_data = {
            "alert_message": "Test alert",
            "channels": ["HTTP"],
            "severity": "HIGH"
        }
        
        with pytest.raises(Exception) as exc_info:
            await agent.call_tool(
                name="HttpNotification",
                arguments=alert_data
            )
        
        # Verify connection error is handled
        error_msg = str(exc_info.value).lower()
        assert any(keyword in error_msg for keyword in ["connection", "timeout", "unavailable", "refused"])

    @pytest.mark.real_service
    async def test_real_agent_mcp_integration(self, alert_agent, http_alert_data):
        """Test that agent properly integrates with MCP server for real calls."""
        input_data = AlertManagementInputSchema(**http_alert_data)
        
        # Verify agent has MCP client configured
        assert hasattr(alert_agent, '_client')
        assert alert_agent._client is not None
        
        # Test MCP tool call routing
        result = await alert_agent.call_tool(
            name="HttpNotification",
            arguments=input_data.model_dump()
        )
        
        # Verify result comes from real MCP server call
        assert result is not None
        assert isinstance(result, dict)
        
        # Validate that this is real data, not mocked
        # Real service should return specific alert ID format
        if "alert_id" in result:
            alert_id = result["alert_id"]
            assert isinstance(alert_id, str)
            assert len(alert_id) > 0
            # Real IDs should have UUID or timestamp format
            assert len(alert_id) >= 8  # Minimum realistic ID length

    @pytest.mark.real_service
    async def test_concurrent_alert_requests(self, alert_agent, http_alert_data, mqtt_alert_data, email_alert_data):
        """Test multiple concurrent alert requests to real service."""
        input_http = AlertManagementInputSchema(**http_alert_data)
        input_mqtt = AlertManagementInputSchema(**mqtt_alert_data)
        input_email = AlertManagementInputSchema(**email_alert_data)
        
        # Execute concurrent requests
        tasks = [
            alert_agent.call_tool("send_alert", input_http.model_dump()),
            alert_agent.call_tool("send_alert", input_mqtt.model_dump()),
            alert_agent.call_tool("send_alert", input_email.model_dump())
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all requests completed successfully
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                pytest.fail(f"Alert request {i} failed: {result}")
            
            assert result is not None
            assert "alert_id" in result
            assert "delivery_status" in result
            assert "channel" in result

    @pytest.mark.real_service
    async def test_alert_delivery_verification(self, alert_agent, http_alert_data):
        """Test verification of alert delivery through real service."""
        input_data = AlertManagementInputSchema(**http_alert_data)
        
        # Send alert and get alert ID
        result = await alert_agent.call_tool(
            name="HttpNotification",
            arguments=input_data.model_dump()
        )
        
        alert_id = result.get("alert_id")
        assert alert_id is not None
        
        # Verify delivery status
        if hasattr(alert_agent, 'check_delivery_status'):
            delivery_status = await alert_agent.call_tool(
                name="check_delivery_status",
                arguments={"alert_id": alert_id}
            )
            
            assert delivery_status is not None
            assert "status" in delivery_status
            assert delivery_status["status"] in ["DELIVERED", "SENT", "SUCCESS", "PENDING"]

    @pytest.mark.real_service
    async def test_question_data_accuracy(self, http_alert_data, mqtt_alert_data, email_alert_data):
        """Test that parsed question data matches expected values from question.md."""
        # Validate HTTP alert data
        assert "alert_message" in http_alert_data
        http_message = http_alert_data["alert_message"].lower()
        assert "url" in str(http_alert_data).lower()  # HTTP should specify URL
        assert "cpu" in http_message
        assert "shortage" in http_message
        
        # Validate MQTT alert data
        assert "alert_message" in mqtt_alert_data
        mqtt_message = mqtt_alert_data["alert_message"].lower()
        assert "mqtt" in str(mqtt_alert_data).lower()  # MQTT should specify protocol
        assert "cpu" in mqtt_message
        assert "shortage" in mqtt_message
        
        # Validate email alert data
        assert "alert_message" in email_alert_data
        email_message = email_alert_data["alert_message"].lower()
        assert "<EMAIL>" in str(email_alert_data).lower()  # Specific email from question.md
        assert "cpu" in email_message
        assert "shortage" in email_message

    @pytest.mark.real_service
    async def test_alert_severity_levels(self, alert_agent):
        """Test different alert severity levels with real service."""
        severity_levels = ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
        
        for severity in severity_levels:
            alert_data = {
                "alert_message": f"Test {severity} severity alert",
                "channels": ["HTTP"],
                "severity": severity
            }
            
            input_data = AlertManagementInputSchema(**alert_data)
            result = await alert_agent.call_tool(
                name="HttpNotification",
                arguments=input_data.model_dump()
            )
            
            assert result is not None
            assert "delivery_status" in result
            
            # Verify severity is properly handled
            if "severity" in result:
                assert result["severity"] == severity

    @pytest.mark.real_service
    async def test_notification_channel_configuration(self, alert_agent):
        """Test that notification channels are properly configured."""
        # Test each channel type individually
        channels = ["HTTP", "MQTT", "EMAIL"]
        
        for channel in channels:
            alert_data = {
                "alert_message": f"Test {channel} channel configuration",
                "channels": [channel],
                "severity": "MEDIUM"
            }
            
            input_data = AlertManagementInputSchema(**alert_data)
            result = await alert_agent.call_tool(
                name="HttpNotification",
                arguments=input_data.model_dump()
            )
            
            assert result is not None
            assert result["channel"] == channel
            assert "delivery_status" in result
            
            # Verify channel-specific configuration
            if "delivery_details" in result:
                details = result["delivery_details"]
                
                if channel == "HTTP":
                    assert "url" in details or "endpoint" in details
                elif channel == "MQTT":
                    assert "topic" in details or "broker" in details
                elif channel == "EMAIL":
                    assert "recipient" in details or "to" in details