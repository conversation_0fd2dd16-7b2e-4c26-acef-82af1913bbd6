"""
Pytest configuration file with shared fixtures for real service testing.

This module provides fixtures for testing with actual agent_develop services,
including service health checks, test data parsing, and agent factory fixtures.
"""

import pytest
import pytest_asyncio
import asyncio
import os
from typing import Dict, Any, Optional
import aiohttp
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent
from config.integration_config import check_service_health
from tests.utils.question_parser import parse_shortage_questions, parse_alert_questions


# Service URL fixtures
@pytest.fixture(scope="session")
def shortage_service_url() -> str:
    """Provide shortage-index service URL for different environments."""
    return os.getenv("SHORTAGE_SERVICE_URL", "http://localhost:6970")


@pytest.fixture(scope="session")
def alert_service_url() -> str:
    """Provide alert-notification service URL for different environments."""
    return os.getenv("ALERT_SERVICE_URL", "http://localhost:6971")


# Service health check fixtures
@pytest_asyncio.fixture(scope="session")
async def shortage_service_health(shortage_service_url: str) -> bool:
    """Check if shortage-index service is available before running tests."""
    try:
        # Try SSE endpoint which is the main endpoint for MCP servers
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{shortage_service_url}/sse", timeout=5) as response:
                # SSE endpoint should respond with 200 and start streaming
                return response.status == 200
    except Exception as e:
        print(f"Shortage service health check failed: {e}")
        return False


@pytest_asyncio.fixture(scope="session")
async def alert_service_health(alert_service_url: str) -> bool:
    """Check if alert-notification service is available before running tests."""
    try:
        # Try SSE endpoint which is the main endpoint for MCP servers
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{alert_service_url}/sse", timeout=5) as response:
                # SSE endpoint should respond with 200 and start streaming
                return response.status == 200
    except Exception as e:
        print(f"Alert service health check failed: {e}")
        return False


# Test data fixtures from question.md files
@pytest.fixture(scope="session")
def shortage_question_data() -> Dict[str, Any]:
    """Parse shortage analysis test data from agent_develop/index/question.md."""
    question_file = Path("/merge/agent_develop/index/question.md")
    if not question_file.exists():
        pytest.skip(f"Question file not found: {question_file}")
    
    return parse_shortage_questions(str(question_file))


@pytest.fixture(scope="session")
def alert_question_data() -> Dict[str, Any]:
    """Parse alert notification test data from agent_develop/notification/question.md."""
    question_file = Path("/merge/agent_develop/notification/question.md")
    if not question_file.exists():
        pytest.skip(f"Question file not found: {question_file}")
    
    return parse_alert_questions(str(question_file))


@pytest.fixture
def basic_shortage_data(shortage_question_data: Dict[str, Any]) -> Dict[str, Any]:
    """Provide basic shortage analysis test data from question.md."""
    return shortage_question_data.get("basic_shortage", {
        "components": {
            "CPU": {"available": 1, "required": 2},
            "GPU": {"available": 2, "required": 6},
            "motherboard": {"available": 1, "required": 3},
            "fans": {"available": 1, "required": 6}
        }
    })


@pytest.fixture
def weighted_shortage_data(shortage_question_data: Dict[str, Any]) -> Dict[str, Any]:
    """Provide weighted shortage analysis test data from question.md."""
    return shortage_question_data.get("weighted_shortage", {
        "components": {
            "CPU": {"available": 1, "required": 2},
            "GPU": {"available": 2, "required": 6},
            "motherboard": {"available": 1, "required": 3},
            "fans": {"available": 1, "required": 6}
        },
        "weights": {
            "CPU": 0.2,
            "GPU": 0.6,
            "motherboard": 0.1,
            "fans": 0.1
        }
    })


@pytest.fixture
def http_alert_data(alert_question_data: Dict[str, Any]) -> Dict[str, Any]:
    """Provide HTTP alert test data from question.md."""
    return alert_question_data.get("http_alert", {
        "alert_message": "cpu matrial shortage alert",
        "channels": ["HTTP"],
        "severity": "HIGH",
        "delivery_config": {
            "http": {
                "url": "http://localhost:8080/alerts"
            }
        }
    })


@pytest.fixture
def mqtt_alert_data(alert_question_data: Dict[str, Any]) -> Dict[str, Any]:
    """Provide MQTT alert test data from question.md."""
    return alert_question_data.get("mqtt_alert", {
        "alert_message": "cpu matrial shortage alert",
        "channels": ["MQTT"],
        "severity": "HIGH",
        "delivery_config": {
            "mqtt": {
                "topic": "alerts/shortage",
                "broker": "localhost:1883"
            }
        }
    })


@pytest.fixture
def email_alert_data(alert_question_data: Dict[str, Any]) -> Dict[str, Any]:
    """Provide email alert test data from question.md."""
    return alert_question_data.get("email_alert", {
        "alert_message": "cpu matrial shortage alert",
        "channels": ["EMAIL"],
        "severity": "HIGH",
        "delivery_config": {
            "email": {
                "recipient": "<EMAIL>",
                "subject": "Material Shortage Alert"
            }
        }
    })


@pytest.fixture
def multi_channel_alert_data(alert_question_data: Dict[str, Any]) -> Dict[str, Any]:
    """Provide multi-channel alert test data combining all channels."""
    return {
        "alert_message": "cpu matrial shortage alert - multi-channel",
        "channels": ["HTTP", "MQTT", "EMAIL"],
        "severity": "CRITICAL",
        "delivery_config": {
            "http": {"url": "http://localhost:8080/alerts"},
            "mqtt": {"topic": "alerts/shortage", "broker": "localhost:1883"},
            "email": {"recipient": "<EMAIL>", "subject": "Critical Shortage Alert"}
        }
    }


# Agent factory fixtures
@pytest_asyncio.fixture
async def shortage_agent(shortage_service_url: str, shortage_service_health: bool):
    """Create shortage analyzer agent with real service configuration."""
    if not shortage_service_health:
        pytest.skip("Shortage service not available")
    
    # Set environment variable for service URL
    os.environ["SHORTAGE_SERVICE_URL"] = shortage_service_url
    
    # Create agent with real service configuration
    agent = create_shortage_analyzer_agent()
    
    # Verify agent is properly configured
    assert agent is not None
    
    yield agent
    
    # Cleanup
    if hasattr(agent, 'cleanup'):
        await agent.cleanup()


@pytest_asyncio.fixture
async def alert_agent(alert_service_url: str, alert_service_health: bool):
    """Create alert manager agent with real service configuration."""
    if not alert_service_health:
        pytest.skip("Alert service not available")
    
    # Set environment variable for service URL
    os.environ["ALERT_SERVICE_URL"] = alert_service_url
    
    # Create agent with real service configuration
    agent = create_alert_manager_agent()
    
    # Verify agent is properly configured
    assert agent is not None
    
    yield agent
    
    # Cleanup
    if hasattr(agent, 'cleanup'):
        await agent.cleanup()


# Environment configuration fixtures
@pytest.fixture(scope="session", autouse=True)
def configure_test_environment():
    """Configure environment variables for testing."""
    # Set test-specific environment variables
    test_env = {
        "ENVIRONMENT": "test",
        "LOG_LEVEL": "DEBUG",
        "MCP_TIMEOUT": "30",
        "SERVICE_RETRY_ATTEMPTS": "3",
        "SERVICE_RETRY_DELAY": "1"
    }
    
    # Store original values
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original environment
    for key, original_value in original_env.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


# Cleanup fixtures
@pytest_asyncio.fixture(autouse=True)
async def cleanup_test_state():
    """Reset agent state and clear test notifications after each test."""
    yield
    
    # Clear any test notifications
    try:
        # Clean up test alerts if cleanup endpoint exists
        async with aiohttp.ClientSession() as session:
            # Try to clean up test data from services
            alert_service_url = os.getenv("ALERT_SERVICE_URL", "http://localhost:6971")
            try:
                async with session.delete(f"{alert_service_url}/test-cleanup", timeout=5):
                    pass  # Ignore response
            except Exception:
                pass  # Service may not have cleanup endpoint
            
            shortage_service_url = os.getenv("SHORTAGE_SERVICE_URL", "http://localhost:6970")
            try:
                async with session.delete(f"{shortage_service_url}/test-cleanup", timeout=5):
                    pass  # Ignore response
            except Exception:
                pass  # Service may not have cleanup endpoint
    except Exception:
        pass  # Cleanup is best effort


# Skip markers for real service tests
def pytest_configure(config):
    """Configure pytest markers for real service tests."""
    config.addinivalue_line(
        "markers", "real_service: mark test as requiring real services to be running"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically skip real service tests when services are not available."""
    # This function could be extended to automatically skip tests
    # based on service availability, but we're handling it in fixtures
    pass


# Helper fixtures for service availability checking
@pytest_asyncio.fixture
async def ensure_services_running(shortage_service_health: bool, alert_service_health: bool):
    """Ensure both services are running before test execution."""
    if not shortage_service_health:
        pytest.skip("Shortage-index service not available")
    if not alert_service_health:
        pytest.skip("Alert-notification service not available")
    return True


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Fixture for measuring test performance."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
            return self.elapsed()
        
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Mock data for fallback when services are unavailable
@pytest.fixture
def mock_shortage_response():
    """Provide mock shortage analysis response for fallback scenarios."""
    return {
        "shortage_index": 0.625,
        "weighted_shortage_index": 0.82,
        "risk_level": "HIGH",
        "component_analysis": {
            "CPU": {"available": 1, "required": 2, "shortage_ratio": 0.5},
            "GPU": {"available": 2, "required": 6, "shortage_ratio": 0.67},
            "motherboard": {"available": 1, "required": 3, "shortage_ratio": 0.67},
            "fans": {"available": 1, "required": 6, "shortage_ratio": 0.83}
        },
        "component_weights": {
            "CPU": 0.2,
            "GPU": 0.6,
            "motherboard": 0.1,
            "fans": 0.1
        }
    }


@pytest.fixture
def mock_alert_response():
    """Provide mock alert response for fallback scenarios."""
    return {
        "alert_id": "test-alert-12345",
        "delivery_status": "SENT",
        "channel": "HTTP",
        "timestamp": "2025-01-01T12:00:00Z",
        "delivery_details": {
            "url": "http://localhost:8080/alerts",
            "response_code": 200
        }
    }