# pyright: reportInvalidTypeForm=false
from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
from rich.console import Console
from rich.table import Table
import openai
import os
import instructor
from pydantic import Field
from atomic_agents.agents.base_agent import BaseIOSchema, BaseAgent, BaseAgentConfig
from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
from atomic_agents.lib.components.agent_memory import AgentMemory
from typing import Union, Type, Dict, Optional
from dataclasses import dataclass

model_name = "meta-llama/Llama-3.3-70B-Instruct"
vLLM_url = "http://************:8701/v1"

# 1. Configuration and environment setup
@dataclass
class MCPConfig:
    """Configuration for the MCP Agent system using SSE transport."""

    mcp_server_url: str = "http://localhost:8702"

    # NOTE: In contrast to other examples, we use gpt-4o and not gpt-4o-mini here.
    # In my tests, gpt-4o-mini was not smart enough to deal with multiple tools like that
    # and at the moment MCP does not yet allow for adding sufficient metadata to
    # clarify tools even more and introduce more constraints.
    openai_model: str = "meta-llama/Llama-3.3-70B-Instruct"
    openai_api_key: str = os.getenv("OPENAI_API_KEY")
    """
    def __post_init__(self):
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
    """

config = MCPConfig()
console = Console()
client = instructor.from_openai(openai.OpenAI(base_url=vLLM_url, api_key="OPENAI_API_KEY"), mode=instructor.Mode.JSON)


class FinalResponseSchema(BaseIOSchema):
    """Schema for providing a final text response to the user."""

    response_text: str = Field(..., description="The final text response to the user's query")


# Fetch tools and build ActionUnion statically
tools = fetch_mcp_tools(
    mcp_endpoint=config.mcp_server_url,
    use_stdio=False,
)
if not tools:
    raise RuntimeError("No MCP tools found. Please ensure the MCP server is running and accessible.")

# Build mapping from input_schema to ToolClass
tool_schema_to_class_map: Dict[Type[BaseIOSchema], Type[BaseAgent]] = {
    ToolClass.input_schema: ToolClass for ToolClass in tools if hasattr(ToolClass, "input_schema")
}
# Collect all tool input schemas
tool_input_schemas = tuple(tool_schema_to_class_map.keys())
print("[Debug]")
print("Tool input schemas:", [schema.__name__ for schema in tool_input_schemas])
# Available schemas include all tool input schemas and the final response schema
available_schemas = tool_input_schemas + (FinalResponseSchema,)
print("[Debug]")
print("Available schemas:", [schema.__name__ for schema in available_schemas])

# Define the Union of all action schemas
ActionUnion = Union[*available_schemas]

def safe_orchestrator_run(agent, *args, **kwargs):
    import traceback

    max_retry = 20
    example_schema = {
        "reasoning": "To list all tables, I will use SHOW TABLES.",
        "action": {
            "tool_name": "mysql_agent",
            "input_data": {
                "query": "SHOW TABLES;"
            }
        }
    }
    for i in range(max_retry):
        try:
            output = agent.run(*args, **kwargs)
            if hasattr(output, "action") and output.action is not None:
                return output
            else:
                agent.memory.add_message(
                    "system",
                    {"query": (
                        "【格式錯誤提醒】Your last output missed the 'action' field!\n"
                        "你的 output 必須同時包含 'reasoning'（推理過程）和 'action'（執行動作），action 必須是 tool input schema 或 FinalResponseSchema。\n"
                        "請嚴格按照下方 JSON 格式產生 output：\n"
                        f"{example_schema}\n"
                        "If you see a validation error, immediately retry with the correct action format.\n"
                    )},
                )
        except Exception as e:
            agent.memory.add_message(
                "system",
                {"query": (
                    "【Validation Error】Your previous output caused a validation error!\n"
                    "錯誤訊息如下（請他媽參考並立即修正 output 格式）：\n"
                    f"{str(e)}\n"
                    "正確格式請參考範例：\n"
                    f"{example_schema}\n"
                )},
            )
    raise RuntimeError("LLM failed to output correct schema after fallback.")

# 2. Schema and class definitions
class MCPOrchestratorInputSchema(BaseIOSchema):
    """Input schema for the MCP Orchestrator Agent."""

    query: str = Field(..., description="The user's query to analyze.")


class OrchestratorOutputSchema(BaseIOSchema):
    """Output schema for the orchestrator. Contains reasoning and the chosen action."""

    reasoning: str = Field(
        ..., description="Detailed explanation of why this action was chosen and how it will address the user's query."
    )
    action: ActionUnion = Field(  # type: ignore[reportInvalidTypeForm]
        ..., description="The chosen action: either a tool's input schema instance or a final response schema instance."
    )


# 3. Main logic and script entry point
def main():
    try:
        console.print("[bold green]Initializing MCP Agent System (SSE mode)...[/bold green]")
        # Display available tools
        table = Table(title="Available MCP Tools", box=None)
        table.add_column("Tool Name", style="cyan")
        table.add_column("Input Schema", style="yellow")
        table.add_column("Description", style="magenta")
        for ToolClass in tools:
            schema_name = ToolClass.input_schema.__name__ if hasattr(ToolClass, "input_schema") else "N/A"
            table.add_row(ToolClass.mcp_tool_name, schema_name, ToolClass.__doc__ or "")
        console.print(table)
        # Create and initialize orchestrator agent
        console.print("[dim]• Creating orchestrator agent...[/dim]")
        memory = AgentMemory()
        orchestrator_agent = BaseAgent(
            BaseAgentConfig(
                client=client,
                model=config.openai_model,
                memory=memory,
                system_prompt_generator=SystemPromptGenerator(
                    background=[
                        "You forget to answer with both reasoning and action.",
                        "You are an MCP Orchestrator Agent, designed to chat with users and",
                        "determine the best way to handle their queries using the available tools.",
                        "You can interact with the tools to execute SQL querie.",
                        "Please check the table's names and their schemas before executing any queries.",
                        """
                        When you call the `mysql_agent` tool, you MUST provide the input as a dictionary with the exact format:
                        {'input_data': {'query': '<YOUR_SQL_QUERY>'}, 'tool_name': 'mysql_agent'}

                        - Only the key 'query' is allowed under 'input_data'.
                        - Do NOT use other keys such as 'sql', 'table', or plain strings.
                        - All SQL statements (e.g., SHOW TABLES, DESCRIBE , SELECT...) must be placed in the value of 'query'.

                        """,
                        """
                        When returning a reasoning step, you must always include an 'action' field. 
                        The 'action' field should be a tool call (e.g., mysql_agent tool call) or a FinalResponseSchema if you are providing the final answer.
                        Makesure to always include both 'reasoning' and 'action' fields in your output.

                        Example:
                        {
                            "reasoning": "To determine the columns available in CUSTOMER_ORDERS, I will use the DESCRIBE statement.",
                            "action": {
                                "tool_name": "mysql_agent",
                                "input_data": {
                                    "query": "DESCRIBE CUSTOMER_ORDERS;"
                                }
                            }
                        },
                        "You can handle material shortage (out-of-stock) analysis for new orders.",
                        "When a user asks if a new order will cause material shortage:",
                        "1. Check the current stock (inventory) for each material required by the order.",
                        "2. If any material's current stock is less than the order's required quantity, a shortage has occurred.",
                        "3. When a shortage is detected, prepare an email including:",
                        "   - material_name",
                        "   - material_code",
                        "   - current_stock",
                        "   - required_quantity for the new order.",
                        "4. If the responsible staff is unknown, always send the email to Jim Xiao.",
                        "5. When sending an email, only output the email content.",
                        "6. Please remember to email the staff if there is a shortage, and confirm to the user if all materials are sufficient. So that they can take action.",
                        """,
                    ],
                    steps=[
                        "1. Use the reasoning field to determine if one or more successive tool calls could be used to handle the user's query.",
                        "2. If so, choose the appropriate tool(s) one at a time and extract all necessary parameters from the query.",
                        "3. If a single tool can not be used to handle the user's query, think about how to break down the query into "
                        "smaller tasks and route them to the appropriate tool(s).",
                        "4. If no sequence of tools could be used, or if you are finished processing the user's query, provide a final "
                        "response to the user.",

                        "5. Receive the user query about potential material shortage for a new order.",
                        "6. Look up required materials and their current stock.",
                        "7. Compare current_stock with the order's required_quantity for each material.",
                        "8. If current_stock < required_quantity, it's a shortage.",
                        "9. For any shortage, prepare and output the email content to notify the staff (to Jim Xiao if unsure).",
                        "10. If no shortage, confirm to the user that all materials are sufficient.",
                    ],
                    output_instructions=[
                        "Every output MUST have both 'reasoning' and 'action' fields, or it will result in a validation error.",
                        "Never omit the 'action' field; if you're done, use FinalResponseSchema.",
                        "You forget to answer with both reasoning and action.",
                        "1. Always provide a detailed explanation of your decision-making process in the 'reasoning' field.",
                        "2. Choose exactly one action schema (either a tool input or FinalResponseSchema).",
                        "3. Ensure all required parameters for the chosen tool are properly extracted and validated.",
                        "4. Maintain a professional and helpful tone in all responses.",
                        "5. Break down complex queries into sequential tool calls before giving the final answer via `FinalResponseSchema`.",
                        "6. Each round, you can only execute one tool call or give a final answer. Do not perform multiple steps at once.",
                        "7. If unsure about table or column names, always use list_tables or describe_table tools first, then construct the next query based on the results.",
                        "8. Never execute a SELECT SQL by guessing; always confirm the schema first.",
                        
                        "9. If you receive an SQL error (e.g., 'table doesn't exist' or 'unknown column'), use list_tables or describe_table to find the correct table or column.",
                        "You must only send valid MySQL SQL queries (such as SHOW TABLES;, DESCRIBE tablename;) when using the mysql_agent tool. Do not use tool names as SQL commands.",
                        "11. Your output MUST be a JSON object with both a 'reasoning' and 'action' field.",
                        "12. The 'action' field must be a tool call (e.g., mysql_agent) with all required parameters, or a FinalResponseSchema if you are answering the user directly.",
                        "13. Never omit the 'action' field, even for intermediate steps such as listing tables or describing a table.",
                        "14. Example: {'reasoning': 'To list all tables...', 'action': {'tool_name': 'mysql_agent', 'input_data': {'query': 'SHOW TABLES;'}}}",
                        "15. If you receive a validation error, immediately retry with the correct action format.",

                        "When a shortage occurs, only output the email notification content. Example:",
                        "Subject: Material Shortage Alert",
                        "To: Jim Xiao",
                        "Body: Material [material_name] ([material_code]) is short for the new order. Current stock: [current_stock]. Required: [required_quantity]. Please arrange replenishment.",
                        "If no shortage, reply to user with normal action.",
                    ],
                ),
                input_schema=MCPOrchestratorInputSchema,
                output_schema=OrchestratorOutputSchema,
            )
        )
        console.print("[green]Successfully created orchestrator agent.[/green]")
        # Interactive chat loop
        console.print("[bold green]MCP Agent Interactive Chat (SSE mode). Type 'exit' or 'quit' to leave.[/bold green]")
        while True:
            query = console.input("[bold yellow]You:[/bold yellow] ").strip()
            if query.lower() in {"exit", "quit"}:
                console.print("[bold red]Exiting chat. Goodbye![/bold red]")
                break
            if not query:
                continue  # Ignore empty input
            try:
                # Initial run with user query
                orchestrator_output =  safe_orchestrator_run(orchestrator_agent, MCPOrchestratorInputSchema(query=query))
                #orchestrator_output = orchestrator_agent.run(MCPOrchestratorInputSchema(query=query))

                action_instance = orchestrator_output.action
                reasoning = orchestrator_output.reasoning
                console.print(f"[cyan]Orchestrator reasoning:[/cyan] {reasoning}")

                # Keep executing until we get a final response
                while not isinstance(action_instance, FinalResponseSchema):
                    schema_type = type(action_instance)
                    ToolClass = tool_schema_to_class_map.get(schema_type)
                    if not ToolClass:
                        raise ValueError(f"Unknown schema type '" f"{schema_type.__name__}" f"' returned by orchestrator")

                    tool_name = ToolClass.mcp_tool_name
                    console.print(f"[blue]Executing tool:[/blue] {tool_name}")
                    console.print(f"[dim]Parameters:[/dim] " f"{action_instance.model_dump()}")

                    tool_instance = ToolClass()
                    tool_output = tool_instance.run(action_instance)
                    console.print(f"[bold green]Result:[/bold green] {tool_output.result}")

                    # Add tool result to agent memory
                    result_message = MCPOrchestratorInputSchema(
                        query=(f"Tool {tool_name} executed with result: " f"{tool_output.result}")
                    )
                    orchestrator_agent.memory.add_message("system", result_message)

                    # Run the agent again without parameters to continue the flow
                    orchestrator_output = safe_orchestrator_run(orchestrator_agent)
                    #orchestrator_output = orchestrator_agent.run()
                    action_instance = orchestrator_output.action
                    reasoning = orchestrator_output.reasoning
                    console.print(f"[cyan]Orchestrator reasoning:[/cyan] {reasoning}")

                # Final response from the agent
                console.print(f"[bold blue]Agent:[/bold blue] {action_instance.response_text}")

            except Exception as e:
                console.print(f"[red]Error processing query:[/red] {str(e)}")
                console.print_exception()
    except Exception as e:
        console.print(f"[bold red]Fatal error:[/bold red] {str(e)}")
        console.print_exception()


if __name__ == "__main__":
    main()