# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an MCP (Model Context Protocol) based database agent system that provides natural language querying capabilities for MySQL databases. The system uses atomic-agents framework with a client-server architecture where users can interact with a MySQL database through natural language queries.

## Architecture

### Core Components

- **Server (`server.py`)**: FastMCP server with SSE transport that hosts database tools and handles client connections
- **Client (`client.py`)**: Interactive chat client that connects to the MCP server and processes user queries through an orchestrator agent
- **Tools Directory**: Contains database interaction tools, primarily the `mysql_agents.py` tool
- **Services Directory**: Database connection management
- **Interfaces Directory**: Base tool interfaces and schemas

### Key Architecture Details

The system uses a two-layer architecture:
1. **MCP Layer**: Server exposes tools via MCP protocol with SSE transport
2. **Agent Layer**: Client uses atomic-agents with an orchestrator pattern to chain tool calls

The orchestrator agent in the client can:
- Execute sequential tool calls to handle complex queries
- Perform material shortage analysis for inventory management
- Send email notifications when shortages are detected
- Handle various database operations (CRUD, table listing, schema inspection)

## Development Setup

### Environment Setup
```bash
# Using uv (preferred)
uv venv .venv
source .venv/bin/activate
uv pip sync
```

### Running the System
```bash
# Start the MCP server (required first)
uv run server.py

# Start the interactive client (in separate terminal)
uv run client.py
```

## Database Configuration

The system connects to MySQL using environment variables with these defaults:
- `MYSQL_HOST`: ************
- `MYSQL_USER`: root  
- `MYSQL_PW`: password
- `MYSQL_DB`: trialDB_01

MySQL web interface available at: http://************:8080

## LLM Configuration

The system uses Llama-3.3-70B-Instruct hosted on vLLM:
- Model: `meta-llama/Llama-3.3-70B-Instruct`
- Endpoint: `http://************:8701/v1`
- Uses instructor library for structured outputs

## Material Shortage Detection Feature

The system includes specialized logic for inventory management:
- Monitors material stock levels against order requirements
- Automatically sends email alerts when shortages are detected
- Default recipient is Jim Xiao when responsible staff is unknown
- Email format includes material name, code, current stock, and required quantity

## Tool System

### Primary Tool: mysql_agent
- **Purpose**: Executes SQL queries on MySQL database
- **Input**: SQL query string
- **Output**: Query results or error messages
- **Usage**: Handles all database operations (SELECT, SHOW TABLES, DESCRIBE, etc.)

### Tool Development Notes
- Tools must inherit from base `Tool` class in `interfaces/tool.py`
- Use proper input/output schemas with pydantic models
- Handle database connections through `MySQL_Service` class
- Follow async/await patterns for tool execution

## Important Development Considerations

- The orchestrator agent has sophisticated error handling and retry logic for malformed outputs
- Always include both 'reasoning' and 'action' fields in agent outputs
- SQL queries must be properly formatted and validated before execution
- The system supports complex multi-step queries that require table inspection before data retrieval
- Memory management is handled through AgentMemory for conversation context

## Testing and Validation

When making changes to database tools or agents:
1. Test with the interactive client using sample queries
2. Verify SQL query generation is correct
3. Test error handling with invalid queries
4. Ensure material shortage detection works with inventory scenarios