import os
import mysql.connector

class MySQL_Service:
    """ connect to MySQL database """
    def __init__(self):
        self.host     = os.getenv("MYSQL_HOST", "************")
        self.user     = os.getenv("MYSQL_USER", "root")
        self.password = os.getenv("MYSQL_PW", "password")
        self.database = os.getenv("MYSQL_DB", "trialDB_01")

    def get_conn(self):
        """ return mysql.connector.Connection """
        return mysql.connector.connect(
            host=self.host,
            user=self.user,
            password=self.password,
            database=self.database,
            autocommit=True,
            charset="utf8mb4",
        )