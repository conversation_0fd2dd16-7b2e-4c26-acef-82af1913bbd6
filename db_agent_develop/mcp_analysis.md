# MCP-Based Communication Patterns Analysis

## Overview
The Model Context Protocol (MCP) implementation in this codebase follows a client-server architecture with Server-Sent Events (SSE) transport for real-time bidirectional communication. The system enables dynamic tool discovery and execution through structured message schemas.

## 1. Message Creation and Publishing

### Message Creation
Messages in the MCP system are created using Pydantic models that follow JSON-RPC 2.0 specification:

**Core Message Types:**
- `JSONRPCRequest`: Request that expects a response
- `JSONRPCNotification`: Notification without expected response  
- `JSONRPCResponse`: Successful response to a request
- `JSONRPCError`: Error response

**Message Structure:**
```python
class JSONRPCRequest(Request[dict[str, Any] | None, str]):
    jsonrpc: Literal["2.0"]
    id: RequestId  
    method: str
    params: dict[str, Any] | None = None
```

**Message Publishing Process:**
1. **Client Side**: Messages are created in `client.py` using the orchestrator agent
2. **Tool Factory**: `MCPToolFactory` generates dynamic tool classes from server definitions
3. **Session Management**: `ClientSession` handles message serialization and transport
4. **Transport Layer**: Messages are sent via SSE (`SseServerTransport`)

### Publishing Workflow
```python
# Message creation in client
orchestrator_output = orchestrator_agent.run(MCPOrchestratorInputSchema(query=query))
action_instance = orchestrator_output.action

# Tool execution triggers message publishing
tool_instance = ToolClass()
tool_output = tool_instance.run(action_instance)
```

## 2. Message Subscription and Routing

### Subscription Mechanism
The system uses Server-Sent Events (SSE) for real-time message subscription:

**Server-Side Subscription Setup:**
```python
# In server.py
async def handle_sse(request: Request) -> None:
    async with sse.connect_sse(
        request.scope,
        request.receive, 
        request._send,
    ) as (read_stream, write_stream):
        await mcp_server.run(
            read_stream,
            write_stream,
            mcp_server.create_initialization_options(),
        )
```

### Message Routing
**Transport Layer Routing:**
- **SSE Endpoint**: `/sse` for establishing persistent connections
- **Message Endpoint**: `/messages` for POST requests containing client messages
- **CORS Middleware**: Handles cross-origin requests with proper headers

**Session-Based Routing:**
```python
# Session message with metadata for routing
@dataclass
class SessionMessage:
    message: JSONRPCMessage
    metadata: MessageMetadata = None

# Server metadata includes request context
@dataclass  
class ServerMessageMetadata:
    related_request_id: RequestId | None = None
    request_context: object | None = None
```

## 3. Message Schemas and Topics

### Tool Input/Output Schemas
**MySQL Agent Tool Schema:**
```python
class MySQLAgentInput(BaseIOSchema):
    query: str = Field(..., description="The SQL query to execute on the MySQL server.")

class MySQLAgentOutput(BaseModel):  
    result: Any = Field(..., description="The query result or error message.")
```

**Dynamic Schema Generation:**
The `MCPToolFactory` dynamically creates tool schemas:
```python
# Input schema creation
InputSchema = self.schema_transformer.create_model_from_schema(
    input_schema_dict,
    f"{tool_name}InputSchema", 
    tool_name,
    f"Input schema for {tool_name}",
)

# Output schema creation  
OutputSchema = type(
    f"{tool_name}OutputSchema", 
    (MCPToolOutputSchema,), 
    {"__doc__": f"Output schema for {tool_name}"}
)
```

### Topic/Queue Structure
**Tool Registration Topics:**
- Tool discovery via `tools/list` endpoint
- Tool execution via specific tool names (e.g., `mysql_agent`)
- Progress notifications via `notifications/progress`

**Message Flow Topics:**
- Initialization: `initialize` → `notifications/initialized`
- Tool calls: `tools/call` with tool-specific parameters
- Results: Response with structured data or errors

## 4. Request-Response and Event Workflows

### Request-Response Pattern
**Standard Request-Response Flow:**
1. Client sends `JSONRPCRequest` with unique ID
2. Server processes request and sends `JSONRPCResponse` with matching ID
3. Error handling via `JSONRPCError` responses

**Tool Execution Request-Response:**
```python
# Tool call request
async def call_tool(self, name: str, arguments: dict[str, Any] | None = None) -> types.CallToolResult:
    return await self.send_request(
        types.ClientRequest(
            types.CallToolRequest(
                method="tools/call",
                params=types.CallToolRequestParams(
                    name=name,
                    arguments=arguments,
                ),
            )
        ),
        types.CallToolResult,
    )
```

### Event-Driven Workflows
**Progress Notifications (Event-based):**
```python
# Progress notification (no response expected)
class ProgressNotification(Notification[ProgressNotificationParams, Literal["notifications/progress"]]):
    method: Literal["notifications/progress"] 
    params: ProgressNotificationParams
```

**Connection Lifecycle Events:**
1. `initialize` - Protocol negotiation
2. `notifications/initialized` - Confirm initialization  
3. Tool discovery and execution
4. Connection termination

## 5. End-to-End Operation Sequence Diagram

### MySQL Query Execution Flow

```mermaid
sequenceDiagram
    participant User
    participant Client as MCP Client (client.py)  
    participant Orchestrator as BaseAgent
    participant Factory as MCPToolFactory
    participant Session as ClientSession
    participant Transport as SSE Transport
    participant Server as MCP Server (server.py)
    participant Tool as MySQLAgentTool
    participant DB as MySQL Database

    User->>Client: Submit query
    Client->>Factory: Fetch MCP tools
    Factory->>Session: Create client session
    Session->>Transport: Establish SSE connection (/sse)
    Transport->>Server: Connect to server
    
    Session->>Server: Send initialize request
    Server->>Session: Return initialize result
    Session->>Server: Send initialized notification
    
    Factory->>Server: Request tool definitions (tools/list)
    Server->>Factory: Return tool schemas
    Factory->>Client: Generate dynamic tool classes
    
    Client->>Orchestrator: Create orchestrator agent
    Orchestrator->>Orchestrator: Analyze user query
    Orchestrator->>Client: Return action (tool call)
    
    Client->>Session: Execute tool call (mysql_agent)
    Session->>Transport: Send POST to /messages
    Transport->>Server: Route to tool handler
    Server->>Tool: Execute with parameters
    Tool->>DB: Execute SQL query
    DB->>Tool: Return query results
    Tool->>Server: Return tool output
    Server->>Transport: Send response
    Transport->>Session: Deliver response  
    Session->>Client: Return tool result
    Client->>Orchestrator: Add result to memory
    Orchestrator->>Client: Generate final response
    Client->>User: Display result
```

### Key Sequence Points:

1. **Connection Establishment**: SSE connection setup with session management
2. **Protocol Negotiation**: Version agreement and capability exchange  
3. **Tool Discovery**: Dynamic schema fetching and class generation
4. **Query Processing**: Multi-step orchestration with tool execution
5. **Response Handling**: Structured result processing and user feedback

## 6. Communication Patterns Summary

### Message Types:
- **Commands**: Tool execution requests with parameters
- **Events**: Progress notifications, lifecycle events
- **Queries**: Tool discovery, schema requests
- **Responses**: Structured results, error messages

### Transport Mechanisms:
- **SSE (Server-Sent Events)**: Real-time server-to-client communication
- **HTTP POST**: Client-to-server message delivery
- **JSON-RPC 2.0**: Message protocol standard

### Data Flow Patterns:
- **Synchronous**: Request-response for tool execution
- **Asynchronous**: Progress notifications and lifecycle events  
- **Bidirectional**: Full duplex communication via SSE + POST
- **Schema-Driven**: Dynamic type safety with Pydantic models

The architecture provides a robust, type-safe communication layer that enables dynamic tool integration while maintaining structured message flows and proper error handling throughout the system.
