#!/usr/bin/env python3
"""
Test script to verify db_agent_develop setup and functionality.
This script tests all major components without requiring the full client-server setup.
"""
import sys
import asyncio
from typing import List

sys.path.append('.')

def test_imports():
    """Test that all required modules can be imported."""
    print("=== Testing Imports ===")
    
    try:
        from services.mysql_connection import MySQL_Service
        print("✓ MySQL service import successful")
    except Exception as e:
        print(f"✗ MySQL service import failed: {e}")
        return False
        
    try:
        from tools.mysql_agents import MySQLAgentTool, MySQLAgentInput
        print("✓ MySQL agent tool import successful")
    except Exception as e:
        print(f"✗ MySQL agent tool import failed: {e}")
        return False
        
    try:
        from interfaces.tool import BaseToolInput, Tool, ToolResponse
         Interface imports successful")print("
    except Exception as e:
        print(f"✗ Interface imports failed: {e}")
        return False
        
    return True

def test_mysql_connection():
    """Test MySQL database connection."""
    print("\n=== Testing MySQL Connection ===")
    
    try:
        from services.mysql_connection import MySQL_Service
        
        service = MySQL_Service()
        print(f"Host: {service.host}")
        print(f"Database: {service.database}")
        print(f"User: {service.user}")
        
        conn = service.get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        
        if result and result[0] == 1:
            print("✓ MySQL connection and query successful")
            
            # Test database tables
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"✓ Found {len(tables)} tables in database")
            print("Tables:", [table[0] for table in tables[:5]], "..." if len(tables) > 5 else "")
            
            cursor.close()
            conn.close()
            return True
        else:
            print("✗ MySQL query returned unexpected result")
            return False
            
    except Exception as e:
        print(f"✗ MySQL connection failed: {e}")
        return False

async def test_mysql_tool():
    """Test MySQL agent tool functionality."""
    print("\n=== Testing MySQL Agent Tool ===")
    
    try:
        from tools.mysql_agents import MySQLAgentTool, MySQLAgentInput
        
        tool = MySQLAgentTool()
        print(f"✓ Tool created: {tool.name}")
        print(f"Description: {tool.description}")
        
        # Test schema retrieval
        schema = tool.get_schema()
        print("✓ Tool schema generated successfully")
        
        # Test simple query
        test_input = MySQLAgentInput(query="SHOW TABLES")
        result = await tool.execute(test_input)
        
        if result and result.content:
            print("✓ Tool execution successful")
            content = result.content[0]
            if content.json_data and 'result' in content.json_data:
                tables_count = len(content.json_data['result'])
                print(f"✓ Query returned {tables_count} tables")
                return True
            else:
                print("✗ Unexpected result format")
                return False
        else:
            print("✗ Tool execution returned no result")
            return False
            
    except Exception as e:
        print(f"✗ MySQL tool test failed: {e}")
        return False

def test_other_tools():
    """Test other available tools."""
    print("\n=== Testing Other Tools ===")
    
    tools_to_test = [
        ('tools.list_tables', 'ListTablesTool'),
        ('tools.describe_tables', 'DescribeTableTool'), 
        ('tools.only_select', 'OnlySelectTool'),
        ('tools.crud', 'CRUDTool')
    ]
    
    successful_imports = 0
    
    for module_path, class_name in tools_to_test:
        try:
            module = __import__(module_path, fromlist=[class_name])
            tool_class = getattr(module, class_name)
            print(f"✓ {class_name} import successful")
            successful_imports += 1
        except Exception as e:
            print(f"✗ {class_name} import failed: {e}")
    
    return successful_imports > 0

def test_server_components():
    """Test server components without actually starting the server."""
    print("\n=== Testing Server Components ===")
    
    try:
        import server
        print("✓ Server module import successful")
        return True
    except Exception as e:
        print(f"✗ Server module import failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("DB Agent Development Environment Setup Test")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("MySQL Connection", test_mysql_connection),
        ("MySQL Tool", test_mysql_tool),
        ("Other Tools", test_other_tools),
        ("Server Components", test_server_components),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The environment is ready for development and testing.")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the setup.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
