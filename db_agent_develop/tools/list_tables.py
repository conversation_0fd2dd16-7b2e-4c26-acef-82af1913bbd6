from pydantic import BaseModel, <PERSON>
from typing import List
from interfaces.tool import BaseToolInput, BaseToolOutput, Tool
from services.mysql_connection import MySQL_Service

class ListTablesInput(BaseToolInput):
    """Input schema for the ListTables tool."""
    pass

class ListTablesOutput(BaseToolOutput):
    """Output schema for the ListTables tool."""
    tables: List[str] = Field(description="List of all table names in the database.")

class ListTablesTool:
    """Tool that lists all table names in the current database."""

    name = "list_tables"
    description = "Lists all tables in the database. No input required."
    input_model = ListTablesInput
    output_model = ListTablesOutput

    def get_schema(self):
        """Get the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }

    def execute(self, input_data: ListTablesInput) -> ListTablesOutput:
        """
        List all table names in the database.

        Args:
            input_data (ListTablesInput): No input required.
        Returns:
            ListTablesOutput: Contains the list of all table names.
        """
        with MySQL_Service().get_conn() as connection, connection.cursor() as cur:
            cur.execute("SHOW TABLES")
            tables = [row[0] for row in cur.fetchall()]
            cur.close()
            connection.close()
            return ListTablesOutput(tables=tables)