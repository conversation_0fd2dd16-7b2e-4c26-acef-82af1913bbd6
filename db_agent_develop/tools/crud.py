from interfaces.tool import BaseToolInput, BaseToolOutput, Tool
from services.mysql_connection import MySQL_Service
from typing import Optional
from pydantic import Field

class CRUDInput(BaseToolInput):
    """
    Input schema for the CRUDTool.

    Attributes:
        query (str): The SQL statement to execute.
    """
    #query: str = Field(..., description="The SQL statement to execute.")
    pass

class CRUDOutput(BaseToolOutput):
    """
    Output schema for the CRUDTool.

    Attributes:
        rows (Optional[list[dict]]): Rows from SELECT result (if any).
        affected (Optional[int]): Number of rows affected (for INSERT/UPDATE/DELETE).
        message (Optional[str]): Status message or error.
    """
    #rows: Optional[list[dict]] = Field(None, description="Rows from SELECT result (if any).")
    #affected: Optional[int] = Field(None, description="Number of rows affected (for INSERT/UPDATE/DELETE).")
    #message: Optional[str] = Field(None, description="Status message or error.")
    pass

class CRUDTool(Tool):
    """Tool for executing any SQL statement (SELECT, INSERT, UPDATE, DELETE, etc.) on the database."""
    name = "crud"
    description = "Execute any SQL statement (SELECT, INSERT, UPDATE, DELETE, etc.)."
    input_schema = CRUDInput
    output_schema = CRUDOutput

    def execute(self, params: CRUDInput, **kwargs) -> CRUDOutput:
        """
        Execute an arbitrary SQL statement.

        Args:
            params (CRUDInput): The input containing the SQL statement.
            **kwargs: Additional keyword arguments (not used).

        Returns:
            CRUDOutput: The output with result rows, affected count, or error message.
        """
        query = params.query.strip()
        with MySQL_Service().get_conn() as conn, conn.cursor(dictionary=True) as cur:
            try:
                # Handle SELECT queries
                if query.lower().startswith("select"):
                    cur.execute(query)
                    rows = cur.fetchall()
                    return CRUDOutput(rows=rows, message="SELECT executed successfully.")
                else:
                    # Handle INSERT/UPDATE/DELETE/other queries
                    affected = cur.execute(query)
                    conn.commit()
                    return CRUDOutput(affected=cur.rowcount, message="Statement executed successfully.")
            except Exception as e:
                # Return error message if any exception occurs
                return CRUDOutput(message=f"Error: {str(e)}")