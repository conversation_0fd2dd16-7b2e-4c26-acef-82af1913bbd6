{"metadata": {"root_path": "/merge/db_agent_develop", "total_files": 23, "categories": {"Other": 8, "Documentation": 3, "Python Modules": 11, "Configuration": 1}}, "files": {".gitignore": {"path": ".giti<PERSON>re", "size": 86, "type": "Other", "description": "Other file type"}, "README.md": {"path": "README.md", "size": 3360, "type": "Documentation", "description": "Project documentation and setup instructions", "headings": ["# mcp_atomic_agents", "## 目錄架構", "## Schema", "## Model", "## MySQL"]}, "client.py": {"path": "client.py", "size": 17051, "type": "Python Module", "description": "", "imports": ["atomic_agents.lib.factories.mcp_tool_factory.fetch_mcp_tools", "rich.console.Console", "rich.table.Table", "openai", "os", "instructor", "pydantic.Field", "atomic_agents.agents.base_agent.BaseIOSchema", "atomic_agents.agents.base_agent.BaseAgent", "atomic_agents.agents.base_agent.BaseAgentConfig", "atomic_agents.lib.components.system_prompt_generator.SystemPromptGenerator", "atomic_agents.lib.components.agent_memory.AgentMemory", "typing.Union", "typing.Type", "typing.Dict", "typing.Optional", "dataclasses.dataclass", "traceback"], "classes": [{"name": "MCPConfig", "docstring": "Configuration for the MCP Agent system using SSE transport."}, {"name": "FinalResponseSchema", "docstring": "Schema for providing a final text response to the user."}, {"name": "MCPOrchestratorInputSchema", "docstring": "Input schema for the MCP Orchestrator Agent."}, {"name": "OrchestratorOutputSchema", "docstring": "Output schema for the orchestrator. Contains reasoning and the chosen action."}], "functions": [{"name": "safe_orchestrator_run", "docstring": ""}, {"name": "main", "docstring": ""}], "entry_points": ["main", "__main__"]}, "pyproject.toml": {"path": "pyproject.toml", "size": 254, "type": "TOML Configuration", "description": "Python project configuration and build system settings", "sections": []}, "server.py": {"path": "server.py", "size": 2815, "type": "Python Module", "description": "", "imports": ["sys", "u<PERSON><PERSON>", "typing.List", "mcp.server.fastmcp.FastMCP", "mcp.server.Server", "mcp.server.sse.SseServerTransport", "starlette.applications.Starlette", "starlette.requests.Request", "starlette.routing.Route", "starlette.routing.Mount", "starlette.middleware.Middleware", "starlette.middleware.cors.CORSMiddleware", "tools.mysql_agents.MySQLAgentTool", "tools.mysql_agents.MySQLAgentInput", "tools.mysql_agents.MySQLAgentOutput", "interfaces.tool.Tool", "interfaces.tool.BaseToolInput", "interfaces.tool.ToolResponse", "interfaces.tool.ToolContent"], "classes": [], "functions": [{"name": "get_available_tools", "docstring": "'Get list of all available tools.'"}, {"name": "create_app", "docstring": ""}], "entry_points": ["__main__"]}, "CLAUDE.md": {"path": "CLAUDE.md", "size": 3907, "type": "Documentation", "description": "CLAUDE.md", "headings": ["# CLAUDE.md", "## Project Overview", "## Architecture", "### Core Components", "### Key Architecture Details"]}, "test_setup.py": {"path": "test_setup.py", "size": 6095, "type": "Python Module", "description": "Python file with syntax errors", "imports": [], "classes": [], "functions": [], "entry_points": []}, "SETUP_VERIFICATION.md": {"path": "SETUP_VERIFICATION.md", "size": 2711, "type": "Documentation", "description": "DB Agent Development Environment Setup - VERIFICATION REPORT", "headings": ["# DB Agent Development Environment Setup - VERIFICATION REPORT", "## ✅ Environment Setup Complete", "### Repository Status", "### Dependencies Installed", "### Database Connection Verified"]}, "inventory_generator.py": {"path": "inventory_generator.py", "size": 13548, "type": "Python Module", "description": "Codebase Inventory Generator", "imports": ["os", "ast", "re", "json", "pathlib.Path", "typing.Dict", "typing.List", "typing.Optional", "typing.Any", "configparser"], "classes": [], "functions": [{"name": "extract_python_info", "docstring": "Extract information from Python files including docstrings, imports, and classes/functions."}, {"name": "extract_config_info", "docstring": "Extract information from configuration files."}, {"name": "extract_markdown_info", "docstring": "Extract information from Markdown files."}, {"name": "extract_sql_info", "docstring": "Extract information from SQL/DDL files."}, {"name": "generate_inventory", "docstring": "Generate a comprehensive inventory of the codebase."}, {"name": "print_inventory_report", "docstring": "Print a formatted inventory report."}], "entry_points": ["__main__"]}, "interfaces/tool.py": {"path": "interfaces/tool.py", "size": 3311, "type": "Python Module", "description": "Interfaces for tool abstractions.", "imports": ["abc.ABC", "abc.abstractmethod", "typing.Any", "typing.Dict", "typing.List", "typing.Optional", "typing.ClassVar", "typing.Type", "typing.TypeVar", "pydantic.BaseModel", "pydantic.Field"], "classes": [{"name": "BaseToolInput", "docstring": "Base class for tool input models."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "docstring": "Model for content in tool responses."}, {"name": "ToolResponse", "docstring": "Model for tool responses."}, {"name": "Tool", "docstring": "Abstract base class for all tools."}], "functions": [{"name": "model_post_init", "docstring": "Post-initialization hook to handle model conversion."}, {"name": "from_model", "docstring": "Create a ToolResponse from a Pydantic model.\n\nThis makes it easier to return structured data directly.\n\nArgs:\n    model: A Pydantic model instance to convert\n\nReturns:\n    A ToolResponse with the model data in JSON format"}, {"name": "from_text", "docstring": "Create a ToolResponse from plain text.\n\nArgs:\n    text: The text content\n\nReturns:\n    A ToolResponse with text content"}, {"name": "get_schema", "docstring": "Get JSON schema for the tool."}], "entry_points": []}, "pictures/Q1.png": {"path": "pictures/Q1.png", "size": 731696, "type": "Other", "description": "Other file type"}, "pictures/Q2.png": {"path": "pictures/Q2.png", "size": 780508, "type": "Other", "description": "Other file type"}, "pictures/Q3.png": {"path": "pictures/Q3.png", "size": 773101, "type": "Other", "description": "Other file type"}, "pictures/Q4.png": {"path": "pictures/Q4.png", "size": 785242, "type": "Other", "description": "Other file type"}, "pictures/demo_shorts.png": {"path": "pictures/demo_shorts.png", "size": 807395, "type": "Other", "description": "Other file type"}, "pictures/io_schema.png": {"path": "pictures/io_schema.png", "size": 119008, "type": "Other", "description": "Other file type"}, "pictures/mysql_material.png": {"path": "pictures/mysql_material.png", "size": 1030022, "type": "Other", "description": "Other file type"}, "services/mysql_connection.py": {"path": "services/mysql_connection.py", "size": 667, "type": "Python Module", "description": "", "imports": ["os", "mysql.connector"], "classes": [{"name": "MySQL_Service", "docstring": "connect to MySQL database "}], "functions": [{"name": "__init__", "docstring": ""}, {"name": "get_conn", "docstring": "return mysql.connector.Connection "}], "entry_points": []}, "tools/crud.py": {"path": "tools/crud.py", "size": 2541, "type": "Python Module", "description": "", "imports": ["interfaces.tool.BaseToolInput", "interfaces.tool.BaseToolOutput", "interfaces.tool.Tool", "services.mysql_connection.MySQL_Service", "typing.Optional", "pydantic.Field"], "classes": [{"name": "CRUDInput", "docstring": "Input schema for the CRUDTool.\n\nAttributes:\n    query (str): The SQL statement to execute."}, {"name": "CRUDOutput", "docstring": "Output schema for the CRUDTool.\n\nAttributes:\n    rows (Optional[list[dict]]): Rows from SELECT result (if any).\n    affected (Optional[int]): Number of rows affected (for INSERT/UPDATE/DELETE).\n    message (Optional[str]): Status message or error."}, {"name": "CRUDTool", "docstring": "Tool for executing any SQL statement (SELECT, INSERT, UPDATE, DELETE, etc.) on the database."}], "functions": [{"name": "execute", "docstring": "Execute an arbitrary SQL statement.\n\nArgs:\n    params (CRUDInput): The input containing the SQL statement.\n    **kwargs: Additional keyword arguments (not used).\n\nReturns:\n    CRUDOutput: The output with result rows, affected count, or error message."}], "entry_points": []}, "tools/describe_tables.py": {"path": "tools/describe_tables.py", "size": 2339, "type": "Python Module", "description": "", "imports": ["typing.Dict", "typing.Any", "typing.Union", "pydantic.Field", "interfaces.tool.BaseToolInput", "interfaces.tool.BaseToolOutput", "interfaces.tool.Tool", "services.mysql_connection.MySQL_Service"], "classes": [{"name": "DescribeTableInput", "docstring": "Input schema for DescribeTableTool.\n\nAttributes:\n    table (str): The name of the table to be described."}, {"name": "DescribeTableOutput", "docstring": "Output schema for DescribeTableTool.\n\nAttributes:\n    columns (list[dict]): The column structure information of the table."}, {"name": "DescribeTableTool", "docstring": "Tool to show the structure of a specific table (columns and their info)."}], "functions": [{"name": "get_schema", "docstring": "Return the JSON schema for this tool."}, {"name": "execute", "docstring": "Execute the tool to describe the structure of a specified table.\n\nArgs:\n    params (DescribeTableInput): The input containing the table name.\n    **kwargs: Additional keyword arguments (not used).\n\nReturns:\n    DescribeTableOutput: The output with column structure information."}], "entry_points": []}, "tools/list_tables.py": {"path": "tools/list_tables.py", "size": 1595, "type": "Python Module", "description": "", "imports": ["pydantic.BaseModel", "pydantic.Field", "typing.List", "interfaces.tool.BaseToolInput", "interfaces.tool.BaseToolOutput", "interfaces.tool.Tool", "services.mysql_connection.MySQL_Service"], "classes": [{"name": "ListTablesInput", "docstring": "Input schema for the ListTables tool."}, {"name": "ListTablesOutput", "docstring": "Output schema for the ListTables tool."}, {"name": "ListTablesTool", "docstring": "Tool that lists all table names in the current database."}], "functions": [{"name": "get_schema", "docstring": "Get the JSON schema for this tool."}, {"name": "execute", "docstring": "List all table names in the database.\n\nArgs:\n    input_data (ListTablesInput): No input required.\nReturns:\n    ListTablesOutput: Contains the list of all table names."}], "entry_points": []}, "tools/mysql_agents.py": {"path": "tools/mysql_agents.py", "size": 1630, "type": "Python Module", "description": "", "imports": ["typing.Any", "typing.Dict", "typing.Union", "pydantic.Field", "pydantic.BaseModel", "pydantic.ConfigDict", "atomic_agents.agents.base_agent.BaseIOSchema", "interfaces.tool.Tool", "interfaces.tool.BaseToolInput", "interfaces.tool.ToolResponse", "services.mysql_connection.MySQL_Service"], "classes": [{"name": "MySQLAgentInput", "docstring": "Input schema for the MySQL agent tool."}, {"name": "MySQLAgentOutput", "docstring": "Output schema for the MySQL agent tool."}, {"name": "MySQLAgentTool", "docstring": "Tool that executes SQL queries on a MySQL database."}], "functions": [], "entry_points": []}, "tools/only_select.py": {"path": "tools/only_select.py", "size": 3414, "type": "Python Module", "description": "", "imports": ["interfaces.tool.BaseToolInput", "interfaces.tool.BaseToolOutput", "interfaces.tool.Tool", "services.mysql_connection.MySQL_Service", "pydantic.Field"], "classes": [{"name": "OnlySelectInput", "docstring": "Input schema for the OnlySelectTool.\n\nAttributes:\n    query (str): The SELECT SQL query to be executed. Must start with 'SELECT'."}, {"name": "OnlySelectOutput", "docstring": "Output schema for the OnlySelectTool.\n\nAttributes:\n    rows (list[dict]): Result rows of the SELECT query."}, {"name": "OnlySelectTool", "docstring": "Tool for executing SQL SELECT statements (read-only).\n\nDescription:\n    This tool executes a SQL SELECT statement.\n    The input MUST be a dictionary with a single key 'query', whose value is a valid SQL SELECT statement as a string.\n    Do NOT use 'table', 'columns', 'where', 'conditions', or any other keys.\n    For example: {'query': 'SELECT job_title FROM EMPLOYEES WHERE full_name = \"<PERSON>\"'}"}], "functions": [{"name": "execute", "docstring": "Execute a SELECT SQL query.\n\nArgs:\n    params (OnlySelectInput or dict): The input containing the SELECT query.\n    **kwargs: Additional keyword arguments (not used).\n\nReturns:\n    OnlySelectOutput: The output containing the query result rows.\n\nRaises:\n    ValueError: If the query does not start with 'SELECT'."}], "entry_points": []}}}