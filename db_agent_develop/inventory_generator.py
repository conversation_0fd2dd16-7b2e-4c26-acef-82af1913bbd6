#!/usr/bin/env python3
"""
Codebase Inventory Generator

This script walks through the directory tree and generates a comprehensive inventory
of packages, modules, configuration files, and other artifacts with descriptions
extracted from docstrings and comments.
"""

import os
import ast
import re
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
import configparser


def extract_python_info(file_path: str) -> Dict[str, Any]:
    """Extract information from Python files including docstrings, imports, and classes/functions."""
    info = {
        'type': 'Python Module',
        'description': '',
        'imports': [],
        'classes': [],
        'functions': [],
        'entry_points': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse AST
        try:
            tree = ast.parse(content)
        except SyntaxError:
            info['description'] = 'Python file with syntax errors'
            return info
        
        # Extract module docstring
        if (ast.get_docstring(tree)):
            info['description'] = ast.get_docstring(tree).split('\n')[0]
        
        # Walk AST for classes, functions, imports
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    info['imports'].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    info['imports'].append(f"{module}.{alias.name}")
            elif isinstance(node, ast.ClassDef):
                class_info = {
                    'name': node.name,
                    'docstring': ast.get_docstring(node) or ''
                }
                info['classes'].append(class_info)
            elif isinstance(node, ast.FunctionDef):
                func_info = {
                    'name': node.name,
                    'docstring': ast.get_docstring(node) or ''
                }
                info['functions'].append(func_info)
                
                # Check for main execution or CLI patterns
                if node.name == 'main' or any(dec.id == 'click.command' for dec in node.decorator_list if hasattr(dec, 'id')):
                    info['entry_points'].append(node.name)
        
        # Check for if __name__ == "__main__" pattern
        if 'if __name__ == "__main__"' in content:
            info['entry_points'].append('__main__')
            
    except Exception as e:
        info['description'] = f'Error parsing Python file: {str(e)}'
    
    return info


def extract_config_info(file_path: str) -> Dict[str, Any]:
    """Extract information from configuration files."""
    info = {'type': 'Configuration File', 'description': '', 'sections': []}
    
    file_ext = Path(file_path).suffix.lower()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract comments for description
        comment_lines = []
        if file_ext in ['.yml', '.yaml']:
            info['type'] = 'YAML Configuration'
            comment_lines = [line.strip()[1:].strip() for line in content.split('\n') 
                           if line.strip().startswith('#') and len(line.strip()) > 1]
        elif file_ext in ['.json']:
            info['type'] = 'JSON Configuration'
        elif file_ext in ['.toml']:
            info['type'] = 'TOML Configuration'
            comment_lines = [line.strip()[1:].strip() for line in content.split('\n') 
                           if line.strip().startswith('#') and len(line.strip()) > 1]
        elif file_ext in ['.cfg', '.ini']:
            info['type'] = 'INI Configuration'
            comment_lines = [line.strip()[1:].strip() for line in content.split('\n') 
                           if line.strip().startswith('#') and len(line.strip()) > 1]
        
        if comment_lines:
            info['description'] = comment_lines[0]
        
        # Special handling for known config files
        filename = Path(file_path).name
        if filename == 'pyproject.toml':
            info['description'] = 'Python project configuration and build system settings'
        elif filename.startswith('requirements'):
            info['description'] = 'Python package dependencies'
        elif filename == 'setup.py':
            info['description'] = 'Python package setup and installation script'
        
    except Exception as e:
        info['description'] = f'Error reading config file: {str(e)}'
    
    return info


def extract_markdown_info(file_path: str) -> Dict[str, Any]:
    """Extract information from Markdown files."""
    info = {'type': 'Documentation', 'description': '', 'headings': []}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Extract first non-empty line or first heading as description
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                info['description'] = line[:100] + ('...' if len(line) > 100 else '')
                break
            elif line.startswith('# '):
                info['description'] = line[2:].strip()
                break
        
        # Extract headings
        headings = [line.strip() for line in lines if line.strip().startswith('#')]
        info['headings'] = headings[:5]  # First 5 headings
        
        # Special handling for common files
        filename = Path(file_path).name.upper()
        if filename == 'README.MD':
            info['description'] = 'Project documentation and setup instructions'
        elif filename == 'CHANGELOG.MD':
            info['description'] = 'Version history and changes log'
        elif filename == 'CONTRIBUTING.MD':
            info['description'] = 'Contribution guidelines and development setup'
            
    except Exception as e:
        info['description'] = f'Error reading markdown file: {str(e)}'
    
    return info


def extract_sql_info(file_path: str) -> Dict[str, Any]:
    """Extract information from SQL/DDL files."""
    info = {'type': 'SQL/DDL File', 'description': '', 'tables': [], 'operations': []}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().upper()
        
        # Extract comments for description
        comment_match = re.search(r'--\s*(.+)', content)
        if comment_match:
            info['description'] = comment_match.group(1).strip()
        
        # Find table operations
        create_tables = re.findall(r'CREATE\s+TABLE\s+(\w+)', content)
        info['tables'].extend(create_tables)
        
        # Find other operations
        operations = re.findall(r'(CREATE|DROP|ALTER|INSERT|UPDATE|DELETE|SELECT)\s+', content)
        info['operations'] = list(set(operations))
        
    except Exception as e:
        info['description'] = f'Error reading SQL file: {str(e)}'
    
    return info


def generate_inventory(root_path: str = '.') -> Dict[str, Any]:
    """Generate a comprehensive inventory of the codebase."""
    inventory = {
        'metadata': {
            'root_path': os.path.abspath(root_path),
            'total_files': 0,
            'categories': {}
        },
        'files': {}
    }
    
    # File type categorization
    categories = {
        'Python Modules': ['.py'],
        'Configuration': ['.yml', '.yaml', '.json', '.toml', '.cfg', '.ini'],
        'Documentation': ['.md', '.rst', '.txt'],
        'SQL/Database': ['.sql', '.ddl'],
        'Scripts': ['.sh', '.bat', '.ps1'],
        'Docker/CI': ['dockerfile', '.dockerignore', '.github', '.gitlab-ci'],
        'Other': []
    }
    
    # Walk directory tree
    for root, dirs, files in os.walk(root_path):
        # Skip common directories to ignore
        dirs[:] = [d for d in dirs if d not in ['.git', '.venv', '__pycache__', '.pytest_cache', 'node_modules']]
        
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, root_path)
            
            # Skip hidden files and common ignore patterns
            if file.startswith('.') and file not in ['.gitignore', '.dockerignore']:
                continue
                
            inventory['metadata']['total_files'] += 1
            
            # Determine file category and extract info
            file_ext = Path(file).suffix.lower()
            file_lower = file.lower()
            
            file_info = {'path': rel_path, 'size': os.path.getsize(file_path)}
            
            if file_ext == '.py':
                file_info.update(extract_python_info(file_path))
                category = 'Python Modules'
            elif file_ext in ['.yml', '.yaml', '.json', '.toml', '.cfg', '.ini'] or file in ['requirements.txt', 'setup.py']:
                file_info.update(extract_config_info(file_path))
                category = 'Configuration'
            elif file_ext in ['.md', '.rst'] or file_ext == '.txt':
                file_info.update(extract_markdown_info(file_path))
                category = 'Documentation'
            elif file_ext in ['.sql', '.ddl']:
                file_info.update(extract_sql_info(file_path))
                category = 'SQL/Database'
            elif file_ext in ['.sh', '.bat', '.ps1'] or file in ['Makefile']:
                file_info.update({'type': 'Script', 'description': 'Executable script or build file'})
                category = 'Scripts'
            elif 'dockerfile' in file_lower or file in ['.dockerignore'] or '.github' in rel_path:
                file_info.update({'type': 'Docker/CI', 'description': 'Container or CI/CD configuration'})
                category = 'Docker/CI'
            else:
                file_info.update({'type': 'Other', 'description': 'Other file type'})
                category = 'Other'
            
            # Add to inventory
            inventory['files'][rel_path] = file_info
            
            # Update category counts
            if category not in inventory['metadata']['categories']:
                inventory['metadata']['categories'][category] = 0
            inventory['metadata']['categories'][category] += 1
    
    return inventory


def print_inventory_report(inventory: Dict[str, Any]):
    """Print a formatted inventory report."""
    print("=" * 80)
    print("CODEBASE INVENTORY REPORT")
    print("=" * 80)
    
    metadata = inventory['metadata']
    print(f"\nRoot Path: {metadata['root_path']}")
    print(f"Total Files: {metadata['total_files']}")
    
    print(f"\nFiles by Category:")
    for category, count in metadata['categories'].items():
        print(f"  {category}: {count}")
    
    print("\n" + "=" * 80)
    print("DETAILED FILE INVENTORY")
    print("=" * 80)
    
    # Group files by category
    files_by_category = {}
    for file_path, file_info in inventory['files'].items():
        category = file_info.get('type', 'Other')
        if category not in files_by_category:
            files_by_category[category] = []
        files_by_category[category].append((file_path, file_info))
    
    # Print each category
    for category, files in files_by_category.items():
        print(f"\n{category.upper()}")
        print("-" * 60)
        
        for file_path, file_info in sorted(files):
            print(f"\n📁 {file_path}")
            print(f"   Type: {file_info.get('type', 'Unknown')}")
            print(f"   Size: {file_info.get('size', 0)} bytes")
            
            description = file_info.get('description', '').strip()
            if description:
                print(f"   Description: {description}")
            
            # Print additional details based on file type
            if 'imports' in file_info and file_info['imports']:
                print(f"   Key Imports: {', '.join(file_info['imports'][:5])}")
            
            if 'classes' in file_info and file_info['classes']:
                class_names = [c['name'] for c in file_info['classes'][:3]]
                print(f"   Classes: {', '.join(class_names)}")
            
            if 'functions' in file_info and file_info['functions']:
                func_names = [f['name'] for f in file_info['functions'][:5]]
                print(f"   Functions: {', '.join(func_names)}")
            
            if 'entry_points' in file_info and file_info['entry_points']:
                print(f"   Entry Points: {', '.join(file_info['entry_points'])}")
            
            if 'headings' in file_info and file_info['headings']:
                print(f"   Headings: {', '.join(file_info['headings'][:3])}")
            
            if 'tables' in file_info and file_info['tables']:
                print(f"   Tables: {', '.join(file_info['tables'])}")
            
            if 'operations' in file_info and file_info['operations']:
                print(f"   SQL Operations: {', '.join(file_info['operations'])}")


if __name__ == "__main__":
    print("Generating codebase inventory...")
    inventory = generate_inventory('.')
    print_inventory_report(inventory)
    
    # Also save to JSON for further processing
    with open('codebase_inventory.json', 'w') as f:
        json.dump(inventory, f, indent=2)
    
    print(f"\n\nInventory also saved to: codebase_inventory.json")
